export class ApiError extends Error {
  code?: string;
  status: number;
  data?: any;
  constructor(message: string, code: string | undefined, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.status = status;
    this.data = data;
  }
}

/**
 * 统一的 fetch JSON 封装：
 * - 自动解析 JSON
 * - 对 { success:false } 或 HTTP 非 2xx 抛出 ApiError（包含 errorCode、status、data）
 */
export async function apiFetchJson<T = any>(input: RequestInfo | URL, init?: RequestInit): Promise<T> {
  const res = await fetch(input as any, init);
  let body: any = null;
  try {
    body = await res.json();
  } catch {
    // ignore
  }

  const isError = !res.ok || (body && body.success === false);
  if (isError) {
    const code = body?.errorCode || 'HTTP_ERROR';
    const message = body?.message || `请求失败，状态码：${res.status}`;
    throw new ApiError(message, code, res.status, body);
  }

  return body as T;
}


export async function apiPostJson<T = any>(url: string, body: any): Promise<T> {
  return apiFetchJson<T>(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
}

