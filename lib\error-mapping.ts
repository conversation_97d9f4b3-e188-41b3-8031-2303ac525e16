export type ErrorMapping = Record<string, string>;

// 统一错误码到用户友好文案的映射（蓝色主题弹窗展示使用）
export const defaultErrorMapping: ErrorMapping = {
  BAD_REQUEST: '请求参数有误，请检查后重试',
  NOT_FOUND: '未找到相关数据',
  NO_LOCAL_PRODUCT: '未找到对应药品，请前往药品管理添加该药品',
  OUT_OF_STOCK: '库存不足，请前往库存管理模块核查',
  ENT_ID_MISSING: '企业ID未设置，请前往系统设置配置后重试',
  UPSTREAM_ERROR: '上游平台返回错误，请稍后重试',
  CONFLICT: '数据冲突，请检查后重试',
  INTERNAL_ERROR: '系统开小差了，请稍后重试',
  HTTP_ERROR: '网络请求失败，请稍后重试'
};

export function mapErrorMessage(code?: string, fallback?: string): string {
  if (!code) return fallback || '请求失败，请稍后重试';
  return defaultErrorMapping[code] || fallback || '请求失败，请稍后重试';
}

