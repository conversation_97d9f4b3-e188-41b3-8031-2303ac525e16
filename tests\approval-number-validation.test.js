import { describe, it, expect } from 'vitest';

// 验证函数
function validateApprovalNumber(approvalNumber) {
  const pattern = /^国药准字[A-Z]\d{8}$/;
  return {
    isValid: pattern.test(approvalNumber),
    message: pattern.test(approvalNumber)
      ? null
      : '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）'
  };
}

const testCases = [
  { input: '国药准字H20123456', expected: { isValid: true, message: null }, description: '标准格式' },
  { input: '国药准字Z20210001', expected: { isValid: true, message: null }, description: '中药' },
  { input: '国药准字S20190999', expected: { isValid: true, message: null }, description: '生物制品' },
  { input: '国药准字J20180888', expected: { isValid: true, message: null }, description: '进口' },

  { input: 'H20123456', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '缺少前缀' },
  { input: '国药准字H2012345', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '位数不足' },
  { input: '国药准字H201234567', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '位数过多' },
  { input: '国药准字20123456', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '缺少字母' },
  { input: '准字H20123456', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '前缀不完整' },
  { input: '国药准字h20123456', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '字母小写' },
  { input: '国药准字H2012345A', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '数字含字母' },
  { input: '', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '空' },
  { input: '国药准字 H20123456', expected: { isValid: false, message: '批准文号格式不正确，应为国药准字+1个字母+8个数字（如：国药准字H20123456）' }, description: '包含空格' },
];

describe('批准文号验证规则', () => {
  testCases.forEach(({ input, expected, description }) => {
    it(description, () => {
      const result = validateApprovalNumber(input);
      expect(result).toEqual(expected);
    });
  });
});
