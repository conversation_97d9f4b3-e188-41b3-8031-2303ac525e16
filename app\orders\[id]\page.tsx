'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';

import { apiFetchJson } from '@/lib/api';
import { mapErrorMessage } from '@/lib/error-mapping';
// 订单类型映射
const orderTypeMap = {
  sales: '销售订单',
  stock_in: '入库订单',
  stock_out: '出库订单',
  inventory: '盘点订单',

  adjustment: '调整订单'
};

// 订单状态映射
const statusMap = {
  pending: '待处理',
  processing: '处理中',
  completed: '已完成',
  cancelled: '已取消',
  draft: '草稿',
  '已完成': '已完成',
  '待处理': '待处理',
  '处理中': '处理中',
  '已取消': '已取消',
  '草稿': '草稿'
};

interface OrderDetail {
  id: string;
  orderNumber: string;
  orderType: string;
  status: string;
  createdAt: string;
  operationDate?: string;

  // 销售订单特有字段
  totalAmount?: number;
  discountAmount?: number;
  payableAmount?: number;
  receivedAmount?: number;
  changeAmount?: number;
  paymentMethod?: string;
  customer?: {
    id: number;
    name: string;
    phone: string;
    address: string;
    isMember: boolean;
    memberNumber: string;
  };
  items?: Array<{
    id: string;
    productId: number;
    productName: string;
    specification: string;
    barcode: string;
    manufacturer: string;
    dosageForm: string;
    quantity: number;
    unitPrice: number;
    subtotal: number;
    discount: number;
  }>;

  // 库存记录特有字段
  operationType?: string;
  quantityChange?: number;
  stockBefore?: number;
  stockAfter?: number;
  product?: {
    id: number;
    name: string;
    specification: string;
    barcode: string;
    manufacturer: string;
    dosageForm: string;
  };
  batchNumber?: string;
  expiryDate?: string;
  costPrice?: number;
  supplier?: {
    id: number;
    name: string;
    contact: string;
    phone: string;
    address: string;
  };
  mashangfangxin?: {
    billNumber: string;
    uploadStatus: string;
    uploadTime: string;
    response: string;
  };
  traceCodes?: Array<{
    id: number;
    traceCode: string;
    operationType: string;
    createdAt: string;
  }>;

  // 通用字段
  operator: string;
  note: string;
}

export default function OrderDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const orderId = params.id as string;
  const orderType = searchParams.get('type') || 'sales';

  const [orderDetail, setOrderDetail] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 加载订单详情
  const loadOrderDetail = async () => {
    try {
      setLoading(true);
      setError('');

      const data = await apiFetchJson<any>(`/api/orders/unified/${orderId}?type=${orderType}`);

      if (data.success) {
        setOrderDetail(data.data);
      } else {
        setError(data.message || '加载订单详情失败');
      }
    } catch (err: any) {
      setError(mapErrorMessage(err?.code, '加载订单详情失败，请重试'));
      console.error('加载订单详情失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderId) {
      loadOrderDetail();
    }
  }, [orderId, orderType]);

  // 格式化金额
  const formatAmount = (amount?: number) => {
    if (amount === null || amount === undefined) return '-';
    return `¥${amount.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 打印订单
  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={loadOrderDetail}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  if (!orderDetail) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">订单不存在</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white shadow-sm border-b print:hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/orders"
                  className="text-blue-600 hover:text-blue-800"
                >
                  ← 返回订单列表
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-blue-700">
                    {orderTypeMap[orderDetail.orderType as keyof typeof orderTypeMap]} 详情
                  </h1>
                  <p className="mt-1 text-sm text-gray-600">
                    订单编号：{orderDetail.orderNumber}
                  </p>
                </div>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handlePrint}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                >
                  打印
                </button>
                {orderDetail.orderType === 'sales' && (
                  <Link
                    href={`/sales/${orderDetail.id}`}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    编辑订单
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 订单基本信息 */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-blue-700">基本信息</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">订单编号</label>
                <div className="mt-1 text-sm text-blue-700 font-mono">{orderDetail.orderNumber}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">订单类型</label>
                <div className="mt-1">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {orderTypeMap[orderDetail.orderType as keyof typeof orderTypeMap]}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">订单状态</label>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    orderDetail.status === '已完成' || orderDetail.status === 'completed'
                      ? 'bg-green-100 text-green-800'
                      : orderDetail.status === '待处理' || orderDetail.status === 'pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : orderDetail.status === '已取消' || orderDetail.status === 'cancelled'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {statusMap[orderDetail.status as keyof typeof statusMap] || orderDetail.status}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">创建时间</label>
                <div className="mt-1 text-sm text-gray-900">{formatDate(orderDetail.createdAt)}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">操作人员</label>
                <div className="mt-1 text-sm text-gray-900">{orderDetail.operator}</div>
              </div>
              {orderDetail.note && (
                <div className="md:col-span-2 lg:col-span-3">
                  <label className="block text-sm font-medium text-gray-700">备注</label>
                  <div className="mt-1 text-sm text-gray-900">{orderDetail.note}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 销售订单特有信息 */}
        {orderDetail.orderType === 'sales' && (
          <>
            {/* 客户信息 */}
            {orderDetail.customer && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">客户信息</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">客户姓名</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.customer.name}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">联系电话</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.customer.phone || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">会员状态</label>
                      <div className="mt-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          orderDetail.customer.isMember ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {orderDetail.customer.isMember ? '会员' : '非会员'}
                        </span>
                      </div>
                    </div>
                    {orderDetail.customer.memberNumber && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">会员号</label>
                        <div className="mt-1 text-sm text-gray-900">{orderDetail.customer.memberNumber}</div>
                      </div>
                    )}
                    {orderDetail.customer.address && (
                      <div className="md:col-span-2 lg:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">地址</label>
                        <div className="mt-1 text-sm text-gray-900">{orderDetail.customer.address}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 金额信息 */}
            <div className="bg-white rounded-lg shadow-sm border mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-blue-700">金额信息</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">商品总额</label>
                    <div className="mt-1 text-lg font-semibold text-blue-700">{formatAmount(orderDetail.totalAmount)}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">折扣金额</label>
                    <div className="mt-1 text-lg font-semibold text-red-600">{formatAmount(orderDetail.discountAmount)}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">应付金额</label>
                    <div className="mt-1 text-lg font-semibold text-green-600">{formatAmount(orderDetail.payableAmount)}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">实收金额</label>
                    <div className="mt-1 text-lg font-semibold text-blue-700">{formatAmount(orderDetail.receivedAmount)}</div>
                  </div>
                  {orderDetail.changeAmount !== undefined && orderDetail.changeAmount > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">找零金额</label>
                      <div className="mt-1 text-lg font-semibold text-gray-600">{formatAmount(orderDetail.changeAmount)}</div>
                    </div>
                  )}
                  {orderDetail.paymentMethod && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">支付方式</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.paymentMethod}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 商品明细 */}
            {orderDetail.items && orderDetail.items.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">商品明细</h2>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                          药品名称
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                          规格
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                          数量
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                          单价
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                          小计
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orderDetail.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                            {item.manufacturer && (
                              <div className="text-xs text-gray-500">{item.manufacturer}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.specification || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatAmount(item.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-700">
                            {formatAmount(item.subtotal)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}

        {/* 库存记录特有信息 */}
        {orderDetail.orderType !== 'sales' && (
          <>
            {/* 药品信息 */}
            {orderDetail.product && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">药品信息</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">药品名称</label>
                      <div className="mt-1 text-sm font-medium text-gray-900">{orderDetail.product.name}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">规格</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.product.specification || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">条形码</label>
                      <div className="mt-1 text-sm text-gray-900 font-mono">{orderDetail.product.barcode || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">生产厂家</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.product.manufacturer || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">剂型</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.product.dosageForm || '-'}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 库存变动信息 */}
            <div className="bg-white rounded-lg shadow-sm border mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-blue-700">库存变动信息</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">操作类型</label>
                    <div className="mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        orderDetail.operationType === '入库' ? 'bg-green-100 text-green-800' :
                        orderDetail.operationType === '出库' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {orderDetail.operationType}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">变动数量</label>
                    <div className={`mt-1 text-lg font-semibold ${
                      Number(orderDetail.quantityChange || 0) > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {Number(orderDetail.quantityChange || 0) > 0 ? '+' : ''}{Number(orderDetail.quantityChange || 0)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">操作前库存</label>
                    <div className="mt-1 text-lg font-semibold text-gray-600">{orderDetail.stockBefore}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">操作后库存</label>
                    <div className="mt-1 text-lg font-semibold text-blue-700">{orderDetail.stockAfter}</div>
                  </div>
                  {orderDetail.batchNumber && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">批次号</label>
                      <div className="mt-1 text-sm text-gray-900 font-mono">{orderDetail.batchNumber}</div>
                    </div>
                  )}
                  {orderDetail.expiryDate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">有效期</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.expiryDate}</div>
                    </div>
                  )}
                  {orderDetail.costPrice !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">成本价</label>
                      <div className="mt-1 text-sm text-gray-900">{formatAmount(orderDetail.costPrice)}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 供应商信息 */}
            {orderDetail.supplier && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">供应商信息</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">供应商名称</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.supplier.name}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">联系人</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.supplier.contact || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">联系电话</label>
                      <div className="mt-1 text-sm text-gray-900">{orderDetail.supplier.phone || '-'}</div>
                    </div>
                    {orderDetail.supplier.address && (
                      <div className="md:col-span-2 lg:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">地址</label>
                        <div className="mt-1 text-sm text-gray-900">{orderDetail.supplier.address}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 追溯码信息 */}
            {orderDetail.traceCodes && orderDetail.traceCodes.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">追溯码信息</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {orderDetail.traceCodes.map((trace, index) => (
                      <div key={index} className="border border-gray-200 rounded-md p-3">
                        <div className="text-sm font-mono text-gray-900">{trace.traceCode}</div>
                        <div className="text-xs text-gray-500 mt-1">{formatDate(trace.createdAt)}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* 码上放心信息 */}
            {orderDetail.mashangfangxin && orderDetail.mashangfangxin.billNumber && (
              <div className="bg-white rounded-lg shadow-sm border mb-6">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-blue-700">码上放心平台信息</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">单据号</label>
                      <div className="mt-1 text-sm text-gray-900 font-mono">{orderDetail.mashangfangxin.billNumber}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">上传状态</label>
                      <div className="mt-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          orderDetail.mashangfangxin.uploadStatus === 'success' ? 'bg-green-100 text-green-800' :
                          orderDetail.mashangfangxin.uploadStatus === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {orderDetail.mashangfangxin.uploadStatus || '未上传'}
                        </span>
                      </div>
                    </div>
                    {orderDetail.mashangfangxin.uploadTime && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">上传时间</label>
                        <div className="mt-1 text-sm text-gray-900">{formatDate(orderDetail.mashangfangxin.uploadTime)}</div>
                      </div>
                    )}
                    {orderDetail.mashangfangxin.response && (
                      <div className="md:col-span-2 lg:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">平台响应</label>
                        <div className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md font-mono">
                          {orderDetail.mashangfangxin.response}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
