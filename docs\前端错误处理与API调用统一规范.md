## 前端错误处理与 API 调用统一规范

- 使用 lib/api.ts 的 apiFetchJson 进行所有 /api/* 请求
- 遇到错误时抛出 ApiError，包含 code(statusCode 对应的 errorCode 或后端返回 errorCode)、status、data
- 以 lib/error-mapping.ts 将 errorCode 转为用户友好文案，在自定义蓝色主题弹窗中展示

推荐用法：
- try { await apiFetchJson('/api/...') } catch (e: any) {
  const msg = mapErrorMessage(e?.code, e?.message); showAlert(msg) }

默认映射：
- BAD_REQUEST：请求参数有误
- NOT_FOUND/NO_LOCAL_PRODUCT：未找到数据/药品（引导去药品管理）
- OUT_OF_STOCK：库存不足（展示当前可用库存 data.currentStock）
- ENT_ID_MISSING：企业ID未设置（引导去系统设置）
- UPSTREAM_ERROR：上游平台错误
- CONFLICT：数据冲突
- INTERNAL_ERROR：系统错误
- HTTP_ERROR：网络错误

迁移进度：
- 已替换：销售新建页、供应商列表、设置页、药品管理页、库存页、AddMedicineModal、DbInitializer
- 待替换：其余页面统一逐步迁移到 apiFetchJson

