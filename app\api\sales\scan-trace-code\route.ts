import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

/**
 * 销售管理中扫描药品追溯码API
 * 扫描追溯码，获取药品信息，并准备出库操作
 * POST /api/sales/scan-trace-code
 */
export async function POST(request: NextRequest) {
  try {
    const { traceCode, orderId } = await request.json();

    if (!traceCode) {
      return NextResponse.json({
        success: false,
        errorCode: 'BAD_REQUEST',
        message: '请提供追溯码'
      }, { status: 400 });
    }

    console.log('销售扫描追溯码:', traceCode);

    // 1. 检查追溯码是否已被使用（已出库）
    const existingRecord = await get(
      `SELECT 
        编号, 药品编号, 操作类型, 操作时间, 销售订单编号
      FROM 药品追溯码记录 
      WHERE 追溯码 = ? AND 操作类型 IN ('出库', '销售')`,
      [traceCode]
    );

    if (existingRecord) {
      return NextResponse.json({
        success: false,
        errorCode: 'CODE_ALREADY_USED',
        message: `该追溯码已被使用（${existingRecord.操作类型}），操作时间：${existingRecord.操作时间}`,
        data: {
          isUsed: true,
          existingRecord
        }
      }, { status: 400 });
    }

    // 2. 调用码上放心平台获取药品信息
    let drugInfo = null;
    try {
      const drugResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getDrugInfoByTraceCode',
          params: { code: traceCode }
        }),
      });

      let respBody: any = null;
      try {
        respBody = await drugResponse.json();
      } catch (_) {
        // ignore parse error
      }

      if (!drugResponse.ok) {
        const msg = respBody?.message || `码上放心接口错误，状态码: ${drugResponse.status}`;
        const errorCode = /企业ID未设置/.test(msg) ? 'ENT_ID_MISSING' : 'UPSTREAM_ERROR';
        return NextResponse.json({ success: false, errorCode, message: msg }, { status: drugResponse.status });
      }

      if (respBody?.success) {
        drugInfo = respBody.data;
      } else {
        const msg = respBody?.message || '获取药品追溯信息失败';
        return NextResponse.json({ success: false, message: msg }, { status: 400 });
      }
    } catch (error) {
      console.error('获取药品追溯信息失败:', error);
      return NextResponse.json({ success: false, message: '获取药品追溯信息失败' }, { status: 500 });
    }

    if (!drugInfo) {
      return NextResponse.json({
        success: false,
        message: '无法获取药品追溯信息，请检查追溯码是否正确'
      }, { status: 400 });
    }

    // 3. 解析药品信息
    const codeFullInfo = drugInfo?.result?.models?.code_full_info_dto?.[0];
    if (!codeFullInfo) {
      return NextResponse.json({
        success: false,
        errorCode: 'BAD_TRACE_FORMAT',
        message: '追溯码信息格式错误'
      }, { status: 400 });
    }

    const drugDetails = codeFullInfo.drug_ent_base_d_t_o || {};
    const batchInfo = {
      batchNo: codeFullInfo.batch_no || '',
      expireDate: codeFullInfo.expire_date || '',
      productionDate: codeFullInfo.production_date || '',
      manufacturer: drugDetails.ent_name || '',
      drugName: drugDetails.physic_name || '',
      specification: drugDetails.pkg_spec_crit || '',
      approvalNumber: drugDetails.approval_licence_no || '',
      barcode: codeFullInfo.barcode || '',
      drug_identification_code: (traceCode && /^\d{7}/.test(traceCode) ? traceCode.substring(0, 7) : '')
    };

    // 4. 在本地数据库中查找对应的药品
    let localProduct = null;
    
    // 优先使用药品标识码（追溯码前7位）匹配本地药品
    if (batchInfo.drug_identification_code) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 药品标识码 = ?',
        [batchInfo.drug_identification_code]
      );
    }

    // 如果通过药品标识码没找到，尝试通过批准文号查找
    if (!localProduct && batchInfo.approvalNumber) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 批准文号 = ?',
        [batchInfo.approvalNumber]
      );
    }

    // 最后兜底通过药品名称模糊匹配
    if (!localProduct && batchInfo.drugName) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 名称 LIKE ? OR 通用名 LIKE ?',
        [`%${batchInfo.drugName}%`, `%${batchInfo.drugName}%`]
      );
    }

    if (!localProduct) {
      return NextResponse.json({
        success: false,
        errorCode: 'NO_LOCAL_PRODUCT',
        message: '在本地数据库中未找到对应的药品，请先在药品管理中添加该药品',
        data: {
          traceCodeInfo: batchInfo,
          suggestions: {
            name: batchInfo.drugName,
            manufacturer: batchInfo.manufacturer,
            specification: batchInfo.specification,
            approvalNumber: batchInfo.approvalNumber,
            barcode: batchInfo.barcode
          }
        }
      }, { status: 404 });
    }

    // 5. 检查库存（从药品库存表或批次表汇总）
    let currentStock = 0;
    try {
      const stockRow = await get(
        'SELECT 当前库存 as qty FROM 药品库存 WHERE 药品编号 = ?',[localProduct.编号]
      );
      if (stockRow && typeof stockRow.qty === 'number') {
        currentStock = stockRow.qty;
      } else {
        const sumRow = await get(
          "SELECT COALESCE(SUM(当前数量),0) as qty FROM 药品批次表 WHERE 药品编号 = ? AND 状态 = 'active'",
          [localProduct.编号]
        );
        currentStock = sumRow?.qty || 0;
      }
    } catch (e) {
      console.warn('库存查询失败，按0处理:', e);
      currentStock = 0;
    }

    if (currentStock <= 0) {
      return NextResponse.json({
        success: false,
        errorCode: 'OUT_OF_STOCK',
        message: `药品 ${localProduct.名称} 库存不足，当前库存：${currentStock}`,
        data: { currentStock }
      }, { status: 400 });
    }

    // 6. 查找对应的批次信息（如果API返回批次号则按批次匹配，否则按即将过期的FIFO批次选择一个可用批次）
    let batchRecord = null as any;
    if (batchInfo.batchNo) {
      batchRecord = await get(
        "SELECT * FROM 药品批次表 WHERE 药品编号 = ? AND 批次号 = ? AND 状态 = 'active' AND 当前数量 > 0",
        [localProduct.编号, batchInfo.batchNo]
      );
    }
    if (!batchRecord) {
      batchRecord = await get(
        "SELECT * FROM 药品批次表 WHERE 药品编号 = ? AND 状态 = 'active' AND 当前数量 > 0 ORDER BY 有效期 ASC LIMIT 1",
        [localProduct.编号]
      );
    }

    // 7. 返回药品信息和批次信息，准备添加到销售订单
    return NextResponse.json({
      success: true,
      message: '追溯码扫描成功',
      data: {
        traceCode,
        localProduct: {
          id: localProduct.编号,
          name: localProduct.名称,
          genericName: localProduct.通用名,
          specification: localProduct.规格,
          manufacturer: localProduct.生产厂家,
          price: localProduct.售价,
          // 不再返回库存数量和条形码，遵循药品信息表去库存化与不持久化追溯码/条码
          approvalNumber: localProduct.批准文号
        },
        batchInfo: {
          ...batchInfo,
          localBatchId: batchRecord?.编号 || null,
          localBatchQuantity: batchRecord?.当前数量 || 0
        },
        canSell: true,
        quantity: 1 // 默认销售数量为1
      }
    });

  } catch (error) {
    console.error('销售扫描追溯码失败:', error);
    return NextResponse.json(
      {
        success: false,
        errorCode: 'INTERNAL_ERROR',
        message: '扫描追溯码失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
