# /api/sales/scan-trace-code 接口测试用例（手工/集成思路）

> 说明：当前仓库未配置 Jest/Vitest 等测试框架（package.json 无 test 脚本）。本文件先给出可执行的手工与集成测试步骤，以及后续若接入 Vitest/Jest 的断言用例草案，便于快速落地自动化。

## 运行前准备
- 启动开发服务：`npm run dev`
- 确保系统设置已配置码上放心开放平台参数，尤其是企业ID（RefEntId）。若未配置，将在接口中收到明确的错误提示。
- 数据库中确保：
  - 存在与扫描追溯码前7位对应的 药品信息.药品标识码
  - 药品库存 或 药品批次表 存在可用库存（当前数量>0）

## 手工测试用例

1. 缺少追溯码参数
   - 请求：POST /api/sales/scan-trace-code，body: {}
   - 期望：400，message 含“请提供追溯码”

2. 企业ID未设置
   - 模拟：系统设置中不配置 码上放心开放平台RefEntId
   - 请求：body: { traceCode: "任意20位数字" }
   - 期望：400，message 含“企业ID未设置”

3. 追溯码已被使用
   - 预置：药品追溯码记录表存在该追溯码且操作类型为“出库/销售”
   - 请求：body: { traceCode: "已使用的追溯码" }
   - 期望：400，data.isUsed=true，message 含“已被使用”

4. 码上放心返回错误
   - 模拟：断开外网或配置无效 AppKey/AppSecret
   - 请求：body: { traceCode: "20位数字" }
   - 期望：非200，错误信息被透传（包含上游 message 或状态码）

5. 无法解析返回结构
   - 模拟：返回结构不含 result.models.code_full_info_dto
   - 期望：400，message 含“追溯码信息格式错误”

6. 本地不存在对应药品
   - 预置：药品信息表无匹配 药品标识码/批准文号/名称
   - 期望：404，message 含“未找到对应的药品”，返回 suggestions

7. 库存不足
   - 预置：药品库存=0，且批次表无当前数量>0
   - 期望：400，message 含“库存不足”

8. 成功返回（按批次号匹配）
   - 预置：码上放心返回 batch_no，且本地同批次 当前数量>0
   - 期望：200，data.batchInfo.localBatchId>0，localBatchQuantity>0

9. 成功返回（FIFO 自动选择）
   - 预置：码上放心无 batch_no，本地存在多个批次，选择有效期最近的 active & 当前数量>0 批次
   - 期望：200，返回该批次的信息

## 自动化测试草案（Vitest/Jest）

- 伪代码示例（以 Vitest 为例）：

```ts
import { describe, it, expect, vi, beforeEach } from 'vitest';

// 可抽象出对 sqlite 的 get/query/run 进行 mock
vi.mock('@/lib/db', () => ({
  get: vi.fn(),
  query: vi.fn(),
  run: vi.fn(),
}));

// mock /api/mashangfangxin 的内部 fetch
global.fetch = vi.fn();

describe('/api/sales/scan-trace-code', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('缺少追溯码参数返回400', async () => {
    // 构造 NextRequest 并调用 POST
    // 断言状态与消息
  });

  it('企业ID未设置错误透传', async () => {
    // mock fetch 返回 { ok:false, status:400, json:{ success:false, message:'企业ID未设置...' } }
    // 断言接口返回同样的错误与状态码
  });

  it('追溯码已被使用', async () => {
    // db.get 返回使用记录
  });

  it('库存不足时返回400', async () => {
    // db.get 对 药品库存 返回 0
    // 对 批次表 汇总返回 0
  });

  it('成功时返回批次localBatchQuantity与localBatchId', async () => {
    // mock fetch 返回合法 drugInfo 结构
    // db.get 命中 药品信息 + 批次记录
  });
});
```

> 后续如需我为项目接入 Vitest 并补齐可执行测试代码，请确认后我会添加依赖与测试脚本，并提供可运行的单测文件。
