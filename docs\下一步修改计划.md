# 下一步修改计划（前端 API 与错误引导深化）

日期：2025-08-13

目标：补齐剩余 fetch → apiFetchJson 替换、统一错误行为引导、封装通用 Hook，提升稳定性与一致性。

一、剩余替换与修复
1) 统一替换剩余 fetch('/api')
   - app/orders/[id]/page.tsx：订单详情 API => 使用 apiFetchJson，并按错误 code 透传给 ErrorModal
   - app/sales/[id]/page.tsx：订单详情 API => 使用 apiFetchJson
   - app/orders/test/page.tsx：测试页中的统计与分页 fetch => 可保留或迁移到 apiFetchJson 以便统一错误处理
   - app/api/sales/scan-trace-code/route.ts：内部调用 /api/mashangfangxin 建议改为直接复用 SDK 客户端或服务内部方法，避免服务间 HTTP；如需保留，至少统一用 apiFetchJson
   - app/api/drug-trace/route.ts：内部调用 /api/mashangfangxin 同上
   - app/inventory/components/BatchManagement.tsx、ExpiryWarning.tsx：替换列表/预警接口的 fetch
   - docs/批次号管理规则说明.md 示例代码同步更新
   - lib/batchNumberUtils.ts 已替换 POST；若有 GET 也同步替换

2) 语法/构建错误修复（本轮已修）
   - app/inventory/page.tsx 导入插入到了接口内 => 已修
   - app/products/components/MedicineForm.tsx 导入插入到 interface 内 => 已修
   - app/api/orders/search/route.ts 变量名冲突（searchParams）=> 已改为 sqlParams，并统一引用

3) 构建失败-外部依赖问题（码上放心 SDK）
   - mashangfangxin 依赖 mime、request、ws 缺失
   - 方案A：在 Next.js API Route 中使用动态导入并隔离到 Node.js 运行时；最小化依赖暴露至客户端
   - 方案B：安装依赖（需要您确认后执行）：npm i mime request ws
   - 方案C：为 SDK 包装一个轻量代理层，仅使用必需的 topClient 功能，删除未用到的 tmc/websocket 依赖
   - 建议：优先A + C，若仍报缺依赖再执行B

二、统一错误行为与体验
1) 将 ErrorActions 继续集成到以下弹窗/提示：
   - app/components/AlertDialog.tsx：新增 code/data props 并在内容中渲染 <ErrorActions />
   - 所有调用 ErrorModal/ErrorDialog 的页面，传递 ApiError 的 code/data，使按钮生效
2) 自定义错误类型枚举与映射完善：
   - ENT_ID_MISSING、NO_LOCAL_PRODUCT、OUT_OF_STOCK、UPSTREAM_ERROR、HTTP_ERROR
   - 对应中英文提示、常见排障说明

三、抽象 Hook 与工具
1) 新增 useApi/useMutation Hook
   - 自动处理 loading/error、调用 apiFetchJson、返回标准化结果
   - 提供 onError 回调，直接弹出 ErrorModal 并带 code/data，引导用户操作
2) 事件遥测/日志
   - 记录关键错误链路（例如 ENT_ID_MISSING 次数、OUT_OF_STOCK 次数）用于后续优化

四、开发者文档补充
- 在 docs/ 中补充：
  - 前端 API 规范与示例
  - 错误码说明与 UI 行为
  - Hook 用法与最佳实践

五、预估工作量与顺序
- 第1天：完成剩余 fetch → apiFetchJson 替换（orders/sales/detail、inventory 组件、docs 示例）与 AlertDialog 集成
- 第2天：useApi/useMutation 抽象与落地、页面替换
- 第3天：mashangfangxin 依赖隔离与优化（A/C 方案），必要时征得同意安装依赖（B）
- 第4天：联调与文档完善

请确认：
- mashangfangxin 是否允许安装依赖 mime request ws？
- app/api/drug-trace 与 app/api/sales/scan-trace-code 是否接受改为直接调用 SDK 客户端（避免内部 HTTP）？

