{"name": "retail_trade_system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@headlessui/react": "^2.2.2", "assert": "^2.1.0", "better-sqlite3": "^11.9.1", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "form-data": "^4.0.2", "https-browserify": "^1.0.0", "mime": "^4.0.7", "next": "15.3.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "request": "^2.88.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string_decoder": "^1.3.0", "url": "^0.11.4", "util": "^0.12.5", "ws": "^8.18.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^3.2.4", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}}