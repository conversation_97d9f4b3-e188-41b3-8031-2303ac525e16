import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('@/lib/db', async () => ({ get: vi.fn(), query: vi.fn(), run: vi.fn() }));
const { get, query, run } = vi.mocked(await import('@/lib/db')) as any;

async function loadStockIn() { return await import('../../app/api/inventory/stock-in/route'); }
async function loadStockOut() { return await import('../../app/api/inventory/stock-out/route'); }

function makeReq(body: any): any { return { json: async () => body }; }

describe('inventory legacy API errorCode', () => {
  beforeEach(() => { vi.resetAllMocks(); });

  it('stock-in 缺少必要参数 -> BAD_REQUEST', async () => {
    const { POST } = await loadStockIn();
    const res: any = await POST(makeReq({ product_id: 1 }));
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });

  it('stock-out 未找到药品 -> NO_LOCAL_PRODUCT', async () => {
    const { POST } = await loadStockOut();
    (query as any).mockResolvedValueOnce([]);
    const res: any = await POST(makeReq({ product_id: 1, quantity: 1, reason: '销售', stock_out_date: '2025-01-01' }));
    const body = await res.json();
    expect(res.status).toBe(404);
    expect(body.errorCode).toBe('NO_LOCAL_PRODUCT');
  });
});

