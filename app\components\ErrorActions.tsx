'use client';

import { useRouter } from 'next/navigation';
import { mapErrorMessage } from '@/lib/error-mapping';

export default function ErrorActions({ code, message, data }: { code?: string; message?: string; data?: any }) {
  const router = useRouter();
  const text = mapErrorMessage(code, message);

  const go = (path: string) => () => router.push(path);

  const actions = [] as { label: string; onClick: () => void }[];
  if (code === 'ENT_ID_MISSING') actions.push({ label: '去系统设置', onClick: go('/settings') });
  if (code === 'NO_LOCAL_PRODUCT') actions.push({ label: '去药品管理', onClick: go('/products') });
  if (code === 'OUT_OF_STOCK') actions.push({ label: '去库存管理', onClick: go('/inventory') });

  return (
    <div className="space-y-2">
      <div className="text-blue-700">{text}</div>
      {code === 'OUT_OF_STOCK' && data?.currentStock !== undefined && (
        <div className="text-sm text-gray-500">当前可用库存：{data.currentStock}</div>
      )}
      {actions.length > 0 && (
        <div className="flex gap-2 pt-1">
          {actions.map((a, i) => (
            <button key={i} onClick={a.onClick} className="px-3 py-1 text-white bg-blue-600 rounded hover:bg-blue-700">
              {a.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

