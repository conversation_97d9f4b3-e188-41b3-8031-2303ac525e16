'use client';

import { useState, useEffect, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import ErrorModal from '../../components/ErrorModal';

import { apiFetchJson } from '@/lib/api';

interface ExpiringBatch {

  id: number;
  product_id: number;
  batch_number: string;
  production_date: string;
  expiry_date: string;
  quantity: number;
  remaining_quantity: number;
  supplier_name: string;
  purchase_price: number;
  product_name: string;
  specification: string;
  dosage_form: string;
  manufacturer: string;
  days_to_expiry: number;
  batch_value: number;
  expiry_status: 'expired' | 'critical' | 'warning';
}

interface ExpiryData {
  expired: ExpiringBatch[];
  critical: ExpiringBatch[];
  warning: ExpiringBatch[];
  total_count: number;
  total_value: number;
}

interface ExpiryWarningProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ExpiryWarning({ isOpen, onClose }: ExpiryWarningProps) {
  const [expiryData, setExpiryData] = useState<ExpiryData>({
    expired: [],
    critical: [],
    warning: [],
    total_count: 0,
    total_value: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'critical' | 'warning' | 'expired'>('critical');
  const [errorModal, setErrorModal] = useState<{show: boolean, message: string, details?: string}>({
    show: false,
    message: '',
    details: ''
  });

  useEffect(() => {
    if (isOpen) {
      fetchExpiringBatches();
    }
  }, [isOpen]);

  const fetchExpiringBatches = async () => {
    try {
      setLoading(true);
      const data = await apiFetchJson<any>('/api/inventory/expiring-batches?days=30');

      if (data.success) {
        setExpiryData(data.data);
      } else {
        setErrorModal({
          show: true,
          message: '获取过期预警数据失败',
          details: data.message
        });
      }
    } catch (error) {
      setErrorModal({
        show: true,
        message: '获取过期预警数据时发生错误',
        details: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusBadge = (status: string, daysToExpiry: number) => {
    if (status === 'expired' || daysToExpiry < 0) {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已过期</span>;
    } else if (daysToExpiry <= 7) {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">紧急</span>;
    } else {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">临期</span>;
    }
  };

  const getCurrentBatches = () => {
    return expiryData[selectedTab] || [];
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            aria-hidden="true"
          />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="mx-auto max-w-6xl w-full bg-white rounded-xl shadow-xl max-h-[90vh] flex flex-col transform overflow-hidden text-left align-middle transition-all">
          {/* 头部 */}
          <div className="p-6 border-b border-gray-200">
            <Dialog.Title className="text-xl font-medium text-blue-700">
              有效期预警管理
            </Dialog.Title>
            <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
              <div className="bg-red-50 p-3 rounded-lg">
                <div className="text-red-600 font-medium">紧急处理</div>
                <div className="text-red-800 text-lg font-bold">{expiryData.critical.length}</div>
                <div className="text-red-600 text-xs">7天内过期</div>
              </div>
              <div className="bg-yellow-50 p-3 rounded-lg">
                <div className="text-yellow-600 font-medium">临期预警</div>
                <div className="text-yellow-800 text-lg font-bold">{expiryData.warning.length}</div>
                <div className="text-yellow-600 text-xs">30天内过期</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-gray-600 font-medium">已过期</div>
                <div className="text-gray-800 text-lg font-bold">{expiryData.expired.length}</div>
                <div className="text-gray-600 text-xs">需要处理</div>
              </div>
            </div>
          </div>

          {/* 标签页 */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setSelectedTab('critical')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === 'critical'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                紧急处理 ({expiryData.critical.length})
              </button>
              <button
                onClick={() => setSelectedTab('warning')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === 'warning'
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                临期预警 ({expiryData.warning.length})
              </button>
              <button
                onClick={() => setSelectedTab('expired')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === 'expired'
                    ? 'border-gray-500 text-gray-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                已过期 ({expiryData.expired.length})
              </button>
            </nav>
          </div>

          {/* 内容区域 */}
          <div className="p-6 flex-grow overflow-auto">
            {loading ? (
              <div className="flex justify-center my-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : getCurrentBatches().length > 0 ? (
              <div className="overflow-hidden rounded-lg border border-gray-200">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">药品信息</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">批次号</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余数量</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">批次价值</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getCurrentBatches().map(batch => (
                      <tr key={batch.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3">
                          <div className="text-sm font-medium text-blue-700">{batch.product_name}</div>
                          <div className="text-xs text-gray-500">{batch.specification} | {batch.dosage_form}</div>
                          <div className="text-xs text-gray-400">{batch.manufacturer}</div>
                        </td>
                        <td className="px-4 py-3 text-sm text-blue-700 font-medium">{batch.batch_number}</td>
                        <td className="px-4 py-3">
                          <div className="text-sm text-gray-600">{formatDate(batch.expiry_date)}</div>
                          <div className="text-xs text-gray-500">
                            {batch.days_to_expiry < 0 ? '已过期' : `${batch.days_to_expiry}天后过期`}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-blue-700 font-medium">{batch.remaining_quantity}</td>
                        <td className="px-4 py-3 text-sm text-gray-600">¥{batch.batch_value.toFixed(2)}</td>
                        <td className="px-4 py-3">{getStatusBadge(batch.expiry_status, batch.days_to_expiry)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无{selectedTab === 'critical' ? '紧急' : selectedTab === 'warning' ? '临期' : '过期'}批次
              </div>
            )}
          </div>

          {/* 底部 */}
          <div className="px-6 py-4 bg-gray-50 rounded-b-xl flex justify-between items-center">
            <div className="text-sm text-gray-600">
              总价值: <span className="font-medium text-blue-700">¥{expiryData.total_value.toFixed(2)}</span>
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              关闭
            </button>
          </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>

        {/* 错误提示模态框 */}
        <ErrorModal
          isOpen={errorModal.show}
          onClose={() => setErrorModal({show: false, message: '', details: ''})}
          message={errorModal.message}
          details={errorModal.details}
        />
      </Dialog>
    </Transition>
  );
}
