import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('@/lib/db', async () => ({ get: vi.fn(), query: vi.fn(), run: vi.fn() }));
const { get, query, run } = vi.mocked(await import('@/lib/db')) as any;

async function loadOrdersRoot() { return await import('../../app/api/orders/route'); }
async function loadOrdersId() { return await import('../../app/api/orders/[id]/route'); }

function makeReq(body: any): any { return { json: async () => body, nextUrl: new URL('http://localhost/api') }; }

describe('orders API errorCode', () => {
  beforeEach(() => { vi.resetAllMocks(); });

  it('POST 无效支付方式 -> BAD_REQUEST', async () => {
    const { POST } = await loadOrdersRoot();
    const res: any = await POST(makeReq({ paymentMethod: 'foo' }));
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });

  it('GET 列表内部异常 -> INTERNAL_ERROR', async () => {
    const { GET } = await loadOrdersRoot();
    (get as any).mockRejectedValueOnce(new Error('db down'));
    const req: any = { nextUrl: new URL('http://localhost/api/orders?page=1&limit=10') };
    const res: any = await GET(req);
    const body = await res.json();
    expect(res.status).toBe(500);
    expect(body.errorCode).toBe('INTERNAL_ERROR');
  });

  it('POST 创建内部异常 -> INTERNAL_ERROR（事务回滚）', async () => {
    const { POST } = await loadOrdersRoot();
    // run BEGIN ok
    (run as any).mockResolvedValueOnce({});
    // 在插入订单时抛出
    (run as any).mockRejectedValueOnce(new Error('insert order failed'));
    // ROLLBACK ok
    (run as any).mockResolvedValueOnce({});

    const reqBody = {
      orderNumber: 'SO-TEST',
      customer: { name: '张三', phone: '13800000000' },
      items: [],
      totalAmount: 0,
      payableAmount: 0,
      receivedAmount: 0,
      paymentMethod: 'cash'
    };
    const res: any = await POST(makeReq(reqBody));
    const body = await res.json();
    expect(res.status).toBe(500);
    expect(body.errorCode).toBe('INTERNAL_ERROR');
  });

  it('PUT /orders/[id] 缺少 items -> BAD_REQUEST', async () => {
    const { PUT } = await loadOrdersId();
    // 先确保找到订单，才能走到 items 校验
    (get as any).mockResolvedValueOnce({ id: 1, status: 'completed' });
    const req: any = makeReq({});
    const res: any = await PUT(req, { params: Promise.resolve({ id: '1' }) } as any);
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });

  it('PUT /orders/[id] 订单不存在 -> NOT_FOUND', async () => {
    const { PUT } = await loadOrdersId();
    const req: any = makeReq({ items: [{ id: 1, name: '药品', price: 1, quantity: 1 }], discountAmount: 0 });
    (get as any).mockResolvedValueOnce(null); // 查询订单是否存在
    const res: any = await PUT(req, { params: Promise.resolve({ id: '999' }) } as any);
    const body = await res.json();
    expect(res.status).toBe(404);
    expect(body.errorCode).toBe('NOT_FOUND');
  });
});

