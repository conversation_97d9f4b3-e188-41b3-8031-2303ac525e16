import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('@/lib/db', async () => ({
  query: vi.fn(),
  run: vi.fn(),
}));

const { query, run } = vi.mocked(await import('@/lib/db')) as any;

async function loadModule() {
  return await import('../../app/api/suppliers/route');
}

describe('/api/suppliers errorCode', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('POST 缺少名称 -> BAD_REQUEST', async () => {
    const { POST } = await loadModule();
    const req: any = { json: async () => ({ name: '' }) };
    const res: any = await POST(req);
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });

  it('POST 名称重复 -> CONFLICT 409', async () => {
    const { POST } = await loadModule();
    (query as any).mockResolvedValueOnce([{ 编号: 1, 名称: 'A' }]);
    const req: any = { json: async () => ({ name: 'A' }) };
    const res: any = await POST(req);
    const body = await res.json();
    expect(res.status).toBe(409);
    expect(body.errorCode).toBe('CONFLICT');
  });

  it('DELETE 未提供ID -> BAD_REQUEST', async () => {
    const { DELETE } = await loadModule();
    const req: any = { url: 'http://localhost/api/suppliers' };
    const res: any = await DELETE(req);
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });
});

