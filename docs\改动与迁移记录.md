# 前端 API 统一封装与错误引导改造记录

日期：2025-08-13

本次改造内容：
- 新增 lib/api.ts：ApiError、apiFetchJson、apiPostJson
- 新增 lib/error-mapping.ts：errorCode → 友好文案映射
- 新增 app/components/ErrorActions.tsx：根据 errorCode 提供“去系统设置/库存/药品管理”等按钮
- 新增 app/components/ErrorHint.tsx：在卡片内展示友好错误与引导
- 修改 ErrorModal、ErrorDialog：新增 props code、data 并内嵌 ErrorActions
- 全量替换多处 fetch('/api') 为 apiFetchJson（详见文件清单）
- 新增/完善单测：见 tests/api/*.spec.ts

替换清单（新增一轮）：
- app/orders/page.tsx：订单列表查询、错误提示
- app/products/page.tsx：识别码类型、获取药品/分类、删除药品、扫码追溯
- app/inventory/page.tsx：库存列表、分类、库存盘点提交、出库提交
- app/inventory/components/InventoryCount.tsx：产品列表
- app/settings/components/DatabaseManagement.tsx：备份列表、初始化、备份、还原
- app/components/SalesBarcodeScanner.tsx：条码查询、追溯查询
- app/components/SalesTraceCodeScanner.tsx：追溯扫描与条码查询
- app/components/AddMedicineModal.tsx：产品列表
- app/db-initializer.tsx：初始化数据库
- lib/batchNumberUtils.ts：验证批次号 API

统一错误引导：
- ENT_ID_MISSING → 去 /settings
- NO_LOCAL_PRODUCT → 去 /products
- OUT_OF_STOCK → 去 /inventory（展示 data.currentStock）
- 其他错误使用 mapErrorMessage 输出友好文案

测试与验证：
- npm run test 全部通过：6 套件 31/31
- 建议页面自测：销售新建、产品列表、库存页、设置-数据库管理、扫码相关组件

