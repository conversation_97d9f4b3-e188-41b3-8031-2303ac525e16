# 错误处理规范

## 统一错误弹窗
- 组件：`app/components/AlertDialog.tsx`
- 遮罩：半透明黑色背景（bg-black bg-opacity-50 + rgba(0,0,0,0.5)）
- 主题：蓝色主题 text-blue-700（错误、信息类提示统一蓝色主题）
- 标题：中文语义（错误/成功/警告/提示）

## 错误展示要求
- 不使用浏览器原生 alert()/confirm()，统一用自定义弹窗
- 错误优先使用清晰、可执行的文案
- 可选附加信息：为用户提供上下文（如建议添加的药品信息）

## 销售单追溯码扫描场景
- 企业ID未设置
  - 文案：“请前往系统设置-码上放心开放平台配置企业ID后重试”
- 未匹配本地药品
  - 文案：“未找到对应药品，请前往药品管理添加该药品”
  - 附带参考信息（若有）：名称/厂家/规格/批准文号
- 库存不足
  - 文案：“库存不足，请前往库存管理模块核查（当前可用库存：X）”

## 前端调用规范
- 建议组件将后端错误结构化后上报父组件统一处理（例如 onError 回调）
- 父组件统一通过 AlertDialog 展示错误信息和参考数据
- 保持蓝色主题（text-blue-700）和半透明遮罩一致

## 后端返回规范
- 统一返回 `{ success, message, data }`
- errorCode 统一见 `docs/API错误码规范.md`
- data 中可选提供辅助信息：`suggestions`、`currentStock` 等，便于前端展示

## 示例代码
- 页面层使用 AlertDialog：
  - 企业ID缺失：引导用户前往系统设置
  - 无本地药品：展示参考信息
  - 库存不足：展示当前可用库存

