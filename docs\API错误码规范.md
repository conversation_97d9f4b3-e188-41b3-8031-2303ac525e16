# API 错误码规范（errorCode）

## 目标
- 为前后端提供统一且可机器识别的错误码，降低前端文案解析复杂度，提升错误处理一致性

## 错误码列表
- BAD_REQUEST：请求参数缺失/格式错误（如缺少 traceCode/code/action 等）
- CODE_ALREADY_USED：追溯码已被使用（出库/销售）
- ENT_ID_MISSING：码上放心企业ID未设置（来自配置或上游返回判定）
- UPSTREAM_ERROR：上游平台（码上放心）调用失败或异常
- BAD_TRACE_FORMAT：追溯码信息结构/格式不符合预期
- NO_LOCAL_PRODUCT：本地未匹配到药品（建议前往药品管理新增）
- OUT_OF_STOCK：库存不足（data.currentStock 可返回当前可用库存）
- INTERNAL_ERROR：服务端内部错误

## 返回结构
- 统一为：`{ success: boolean, errorCode?: string, message: string, data?: any }`
- 说明：
  - success = false 时应提供 errorCode 与可阅读 message
  - data 可选提供建议字段（如 suggestions、currentStock），便于前端展示

## 实施范围
- 已实施
  - /api/sales/scan-trace-code：全链路 errorCode
  - /api/products/barcode：BAD_REQUEST、NO_LOCAL_PRODUCT、INTERNAL_ERROR
  - /api/mashangfangxin：BAD_REQUEST、ENT_ID_MISSING、UPSTREAM_ERROR
- 建议扩展
  - 其它 API 按上述规范统一返回结构与 errorCode

## 前端处理建议
- 优先根据 errorCode 分支处理；message 仅用于展示
- 企业ID缺失：提示并引导前往系统设置
- NO_LOCAL_PRODUCT：展示建议信息（名称/厂家/规格/批准文号）
- OUT_OF_STOCK：展示当前可用库存并引导库存管理核查

## 示例
```json
{
  "success": false,
  "errorCode": "OUT_OF_STOCK",
  "message": "药品 XX 库存不足，当前库存：0",
  "data": { "currentStock": 0 }
}
```

