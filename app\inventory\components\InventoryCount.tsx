'use client';

import React, { useState, useEffect, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import ErrorModal from '../../components/ErrorModal';

interface Product {
  id: number;
  name: string;
  stock_quantity: number;
  dosage_form: string;
  storage_condition: string;
}
import { apiFetchJson } from '@/lib/api';
import { mapErrorMessage } from '@/lib/error-mapping';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

interface InventoryCountProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

interface CountItem {
  product_id: number;
  product_name: string;
  system_quantity: number;
  actual_quantity: number;
  difference: number;
  notes: string;
  dosage_form: string;
  storage_condition: string;
}

export default function InventoryCount({ isOpen, onClose, onSubmit }: InventoryCountProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [countItems, setCountItems] = useState<CountItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<{id: number, name: string}[]>([]);
  const [currentTab, setCurrentTab] = useState<'all' | 'discrepancy'>('all');
  const [errorModal, setErrorModal] = useState<{show: boolean, message: string, details?: string}>({
    show: false,
    message: '',
    details: ''
  });

  useEffect(() => {
    if (isOpen) {
      fetchProducts();
      fetchCategories();
    }
  }, [isOpen]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await apiFetchJson<ApiResponse<Product[]>>('/api/products');
      if (data.success && data.data) {
        const products = data.data;
        setProducts(products);

        // 初始化盘点项目
        const items = products.map((p: Product) => ({
          product_id: p.id,
          product_name: p.name || '',
          system_quantity: p.stock_quantity || 0,
          actual_quantity: p.stock_quantity || 0, // 默认与系统数量相同
          difference: 0,
          notes: '',
          dosage_form: p.dosage_form || '',
          storage_condition: p.storage_condition || ''
        }));
        setCountItems(items);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
      setErrorModal({
        show: true,
        message: '获取药品数据失败',
        details: error instanceof Error ? error.message : '请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json() as ApiResponse<{id: number, name: string}[]>;
      if (data.success && data.data) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
      setErrorModal({
        show: true,
        message: '获取分类数据失败',
        details: error instanceof Error ? error.message : '请重试'
      });
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleActualQuantityChange = (productId: number, value: string) => {
    const actualQuantity = value === '' ? 0 : parseInt(value) || 0;

    setCountItems(prevItems =>
      prevItems.map(item => {
        if (item.product_id === productId) {
          const systemQuantity = item.system_quantity || 0;
          const difference = actualQuantity - systemQuantity;
          return { ...item, actual_quantity: actualQuantity, difference };
        }
        return item;
      })
    );
  };

  const handleNotesChange = (productId: number, value: string) => {
    setCountItems(prevItems =>
      prevItems.map(item => {
        if (item.product_id === productId) {
          return { ...item, notes: value };
        }
        return item;
      })
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 只提交有差异的项目
    const itemsToSubmit = countItems.filter(item => item.difference !== 0);

    if (itemsToSubmit.length === 0) {
      setErrorModal({
        show: true,
        message: '没有发现任何库存差异',
        details: '不需要调整'
      });
      return;
    }

    try {
      setLoading(true);
      await onSubmit({
        items: itemsToSubmit,
        count_date: new Date().toISOString().split('T')[0],
        notes: '库存盘点调整'
      });
    } catch (error) {
      console.error('提交盘点数据失败:', error);
      setErrorModal({
        show: true,
        message: '提交盘点数据失败',
        details: error instanceof Error ? error.message : '请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 过滤显示的项目
  const filteredItems = countItems.filter(item => {
    const matchesSearch =
      (item.product_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.dosage_form?.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !selectedCategory ||
      products.find(p => p.id === item.product_id)?.id.toString() === selectedCategory;

    const matchesTab = currentTab === 'all' || (currentTab === 'discrepancy' && item.difference !== 0);

    return matchesSearch && matchesCategory && matchesTab;
  });

  return (
    <>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
              aria-hidden="true"
            />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="mx-auto max-w-5xl w-full bg-white rounded-xl shadow-xl max-h-[90vh] flex flex-col transform overflow-hidden text-left align-middle transition-all">
                  <div className="p-6 border-b border-gray-200">
                    <Dialog.Title className="text-xl font-medium leading-6 text-blue-700">
                      库存盘点
                    </Dialog.Title>
                  </div>

          <div className="p-6 flex-grow overflow-auto">

            <div className="mb-6 flex flex-wrap gap-4">
              <div className="flex-1 min-w-[250px]">
                <label className="block text-sm font-medium text-black mb-1">
                  搜索药品
                </label>
                <input
                  type="text"
                  placeholder="搜索药品名称或剂型..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-black"
                />
              </div>
              <div className="w-48">
                <label className="block text-sm font-medium text-black mb-1">
                  药品分类
                </label>
                <select
                  value={selectedCategory}
                  onChange={handleCategoryChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50 text-black"
                >
                  <option value="">全部分类</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="w-48">
                <label className="block text-sm font-medium text-black mb-1">
                  显示模式
                </label>
                <div className="flex rounded-md overflow-hidden border border-gray-300">
                  <button
                    type="button"
                    className={`flex-1 px-3 py-2 text-sm font-medium ${currentTab === 'all' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'} hover:bg-blue-50`}
                    onClick={() => setCurrentTab('all')}
                  >
                    全部药品
                  </button>
                  <button
                    type="button"
                    className={`flex-1 px-3 py-2 text-sm font-medium ${currentTab === 'discrepancy' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'} hover:bg-blue-50`}
                    onClick={() => setCurrentTab('discrepancy')}
                  >
                    仅显示差异
                  </button>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center my-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredItems.length > 0 ? (
              <form onSubmit={handleSubmit}>
                <div className="overflow-hidden rounded-lg border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">药品名称</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剂型</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">系统库存</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">实际库存</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">差异</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredItems.map(item => (
                        <tr key={item.product_id} className={item.difference !== 0 ? 'bg-yellow-50' : ''}>
                          <td className="px-4 py-3 text-sm text-gray-900">{item.product_name}</td>
                          <td className="px-4 py-3 text-sm text-gray-500">{item.dosage_form}</td>
                          <td className="px-4 py-3 text-sm text-blue-700 font-medium">{item.system_quantity || 0}</td>
                          <td className="px-4 py-3">
                            <input
                              type="number"
                              min="0"
                              value={item.actual_quantity || 0}
                              onChange={(e) => handleActualQuantityChange(item.product_id, e.target.value)}
                              className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                            />
                          </td>
                          <td className={`px-4 py-3 text-sm font-medium ${item.difference > 0 ? 'text-green-600' : item.difference < 0 ? 'text-red-600' : 'text-gray-500'}`}>
                            {item.difference > 0 ? '+' : ''}{item.difference}
                          </td>
                          <td className="px-4 py-3">
                            <input
                              type="text"
                              placeholder="原因备注..."
                              value={item.notes || ''}
                              onChange={(e) => handleNotesChange(item.product_id, e.target.value)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-5 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={loading || countItems.every(item => item.difference === 0)}
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                </div>
              </form>
            ) : (
              <div className="text-center py-8 text-gray-500">
                没有找到符合条件的药品
              </div>
            )}
          </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* 错误提示模态框 */}
      <ErrorModal
        isOpen={errorModal.show}
        onClose={() => setErrorModal({show: false, message: '', details: ''})}
        title="操作失败"
        message={errorModal.message}
        details={errorModal.details}
      />
    </>
  );
}