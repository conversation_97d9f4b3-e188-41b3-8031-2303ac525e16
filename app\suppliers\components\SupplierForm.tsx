'use client';

import React, { useState, useEffect } from 'react';
import { Supplier, SupplierFormData, COOPERATION_STATUS_OPTIONS, STATUS_OPTIONS, SUPPLIER_VALIDATION_RULES } from '@/types/supplier';
import ConfirmDeleteDialog from '@/app/components/ConfirmDeleteDialog';

interface SupplierFormProps {
  isOpen: boolean;
  supplier?: Supplier | null;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
  onClose: () => void;
}

export default function SupplierForm({ isOpen, supplier, onSuccess, onError, onClose }: SupplierFormProps) {
  const [formData, setFormData] = useState<SupplierFormData>({
    name: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    tax_number: '',
    bank_account: '',
    bank_name: '',
    license_number: '',
    license_expiry_date: '',
    gsp_certificate: '',
    gsp_expiry_date: '',
    business_scope: '',
    quality_officer: '',
    cooperation_status: 'active',
    status: 'active',
    notes: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof SupplierFormData, string>>>({});
  const [loading, setLoading] = useState(false);

  // 取消确认对话框状态
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (supplier) {
      // 编辑模式：使用供应商数据
      setFormData({
        name: supplier.name || '',
        contact_person: supplier.contact_person || '',
        phone: supplier.phone || '',
        email: supplier.email || '',
        address: supplier.address || '',
        tax_number: supplier.tax_number || '',
        bank_account: supplier.bank_account || '',
        bank_name: supplier.bank_name || '',
        license_number: supplier.license_number || '',
        license_expiry_date: supplier.license_expiry_date || '',
        gsp_certificate: supplier.gsp_certificate || '',
        gsp_expiry_date: supplier.gsp_expiry_date || '',
        business_scope: supplier.business_scope || '',
        quality_officer: supplier.quality_officer || '',
        cooperation_status: supplier.cooperation_status || 'active',
        status: supplier.status || 'active',
        notes: supplier.notes || ''
      });
    } else {
      // 新增模式：重置表单为空
      setFormData({
        name: '',
        contact_person: '',
        phone: '',
        email: '',
        address: '',
        tax_number: '',
        bank_account: '',
        bank_name: '',
        license_number: '',
        license_expiry_date: '',
        gsp_certificate: '',
        gsp_expiry_date: '',
        business_scope: '',
        quality_officer: '',
        cooperation_status: 'active',
        status: 'active',
        notes: ''
      });
    }

    // 清除错误信息
    setErrors({});
  }, [supplier]);

  // 监听模态框打开状态，确保每次打开时都正确初始化
  useEffect(() => {
    if (isOpen && !supplier) {
      // 新增模式：确保表单为空
      setFormData({
        name: '',
        contact_person: '',
        phone: '',
        email: '',
        address: '',
        tax_number: '',
        bank_account: '',
        bank_name: '',
        license_number: '',
        license_expiry_date: '',
        gsp_certificate: '',
        gsp_expiry_date: '',
        business_scope: '',
        quality_officer: '',
        cooperation_status: 'active',
        status: 'active',
        notes: ''
      });
      setErrors({});
    }
  }, [isOpen, supplier]);

  // 验证单个字段
  const validateField = (field: keyof SupplierFormData, value: string): string | null => {
    const rules: any = SUPPLIER_VALIDATION_RULES as any;
    if (!Object.prototype.hasOwnProperty.call(rules, field)) return null;
    const rule = rules[field as keyof typeof SUPPLIER_VALIDATION_RULES];

    // 必填字段验证
    if (rule.required && (!value || !value.trim())) {
      return rule.message;
    }

    // 如果字段为空且非必填，跳过其他验证
    if (!rule.required && (!value || !value.trim())) {
      return null;
    }

    // 字符串长度验证
    if (rule.minLength && value.length < rule.minLength) {
      return rule.message;
    }
    if (rule.maxLength && value.length > rule.maxLength) {
      return rule.message;
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(value)) {
      return rule.message;
    }

    return null;
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof SupplierFormData, string>> = {};

    // 验证所有字段
    (Object.keys(SUPPLIER_VALIDATION_RULES) as Array<keyof SupplierFormData>).forEach((fieldKey) => {
      const error = validateField(fieldKey, formData[fieldKey] as string);
      if (error) {
        newErrors[fieldKey] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (field: keyof SupplierFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // 实时验证该字段
    const fieldError = validateField(field, value);
    if (fieldError) {
      setErrors(prev => ({ ...prev, [field]: fieldError }));
    }
  };

  // 格式化验证错误消息
  const formatValidationErrors = (errors: Partial<SupplierFormData>): string => {
    const errorMessages = Object.values(errors).filter(Boolean);
    if (errorMessages.length === 0) return '';

    if (errorMessages.length === 1) {
      return errorMessages[0];
    }

    return `表单验证失败，请检查以下字段：\n${errorMessages.map(msg => `• ${msg}`).join('\n')}`;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // 显示验证错误弹窗
      const errorMessage = formatValidationErrors(errors as Partial<SupplierFormData>);
      onError(errorMessage);
      return;
    }

    setLoading(true);

    try {
      const url = supplier ? '/api/suppliers' : '/api/suppliers';
      const method = supplier ? 'PUT' : 'POST';
      const requestData = supplier ? { ...formData, id: supplier.id } : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (result.success) {
        onSuccess(result.message || (supplier ? '供应商更新成功' : '供应商创建成功'));
      } else {
        onError(result.message || result.error || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      onError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    if (JSON.stringify(formData) !== JSON.stringify(supplier ? {
      name: supplier.name || '',
      contact_person: supplier.contact_person || '',
      phone: supplier.phone || '',
      email: supplier.email || '',
      address: supplier.address || '',
      tax_number: supplier.tax_number || '',
      bank_account: supplier.bank_account || '',
      bank_name: supplier.bank_name || '',
      license_number: supplier.license_number || '',
      license_expiry_date: supplier.license_expiry_date || '',
      gsp_certificate: supplier.gsp_certificate || '',
      gsp_expiry_date: supplier.gsp_expiry_date || '',
      business_scope: supplier.business_scope || '',
      quality_officer: supplier.quality_officer || '',
      cooperation_status: supplier.cooperation_status || 'active',
      status: supplier.status || 'active',
      notes: supplier.notes || ''
    } : {
      name: '',
      contact_person: '',
      phone: '',
      email: '',
      address: '',
      tax_number: '',
      bank_account: '',
      bank_name: '',
      license_number: '',
      license_expiry_date: '',
      gsp_certificate: '',
      gsp_expiry_date: '',
      business_scope: '',
      quality_officer: '',
      cooperation_status: 'active',
      status: 'active',
      notes: ''
    })) {
      // 有未保存的修改，显示确认对话框
      setShowCancelConfirm(true);
    } else {
      // 没有修改，直接关闭
      onClose();
    }
  };

  // 确认取消并关闭表单
  const handleConfirmCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center p-4 z-50"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(2px)'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          handleCancel();
        }
      }}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-blue-700">
              {supplier ? '编辑供应商' : '添加供应商'}
            </h2>
            <button
              onClick={handleCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-semibold text-blue-700 mb-4">基本信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    供应商名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入供应商名称"
                  />
                  {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    联系人 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.contact_person}
                    onChange={(e) => handleInputChange('contact_person', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.contact_person ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入联系人姓名"
                  />
                  {errors.contact_person && <p className="text-red-500 text-xs mt-1">{errors.contact_person}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    联系电话 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.phone ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入联系电话"
                  />
                  {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    邮箱地址
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.email ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入邮箱地址"
                  />
                  {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  地址
                </label>
                <textarea
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  placeholder="请输入详细地址"
                />
              </div>
            </div>

            {/* 财务信息 */}
            <div>
              <h3 className="text-lg font-semibold text-blue-700 mb-4">财务信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    税号
                  </label>
                  <input
                    type="text"
                    value={formData.tax_number}
                    onChange={(e) => handleInputChange('tax_number', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.tax_number ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入税号"
                  />
                  {errors.tax_number && <p className="text-red-500 text-xs mt-1">{errors.tax_number}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    开户行
                  </label>
                  <input
                    type="text"
                    value={formData.bank_name}
                    onChange={(e) => handleInputChange('bank_name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    placeholder="请输入开户行"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    银行账户
                  </label>
                  <input
                    type="text"
                    value={formData.bank_account}
                    onChange={(e) => handleInputChange('bank_account', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    placeholder="请输入银行账户"
                  />
                </div>
              </div>
            </div>

            {/* 证照信息 */}
            <div>
              <h3 className="text-lg font-semibold text-blue-700 mb-4">证照信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    营业执照号
                  </label>
                  <input
                    type="text"
                    value={formData.license_number}
                    onChange={(e) => handleInputChange('license_number', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.license_number ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入营业执照号"
                  />
                  {errors.license_number && <p className="text-red-500 text-xs mt-1">{errors.license_number}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    执照有效期
                  </label>
                  <input
                    type="date"
                    value={formData.license_expiry_date}
                    onChange={(e) => handleInputChange('license_expiry_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    GSP证书号
                  </label>
                  <input
                    type="text"
                    value={formData.gsp_certificate}
                    onChange={(e) => handleInputChange('gsp_certificate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700 ${
                      errors.gsp_certificate ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入GSP证书号"
                  />
                  {errors.gsp_certificate && <p className="text-red-500 text-xs mt-1">{errors.gsp_certificate}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    GSP有效期
                  </label>
                  <input
                    type="date"
                    value={formData.gsp_expiry_date}
                    onChange={(e) => handleInputChange('gsp_expiry_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                </div>
              </div>
            </div>

            {/* 业务信息 */}
            <div>
              <h3 className="text-lg font-semibold text-blue-700 mb-4">业务信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    质量负责人
                  </label>
                  <input
                    type="text"
                    value={formData.quality_officer}
                    onChange={(e) => handleInputChange('quality_officer', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    placeholder="请输入质量负责人姓名"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    合作状态
                  </label>
                  <select
                    value={formData.cooperation_status}
                    onChange={(e) => handleInputChange('cooperation_status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  >
                    {COOPERATION_STATUS_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    启用状态
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  >
                    {STATUS_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  经营范围
                </label>
                <textarea
                  value={formData.business_scope}
                  onChange={(e) => handleInputChange('business_scope', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  placeholder="请输入经营范围"
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  备注
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  placeholder="请输入备注信息"
                />
              </div>
            </div>

            {/* 按钮区域 */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                disabled={loading}
              >
                {loading ? '保存中...' : '保存'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* 取消确认对话框 */}
      <ConfirmDeleteDialog
        isOpen={showCancelConfirm}
        onClose={() => setShowCancelConfirm(false)}
        onConfirm={handleConfirmCancel}
        title="确认取消"
        message="表单内容已修改，确定要取消吗？"
        confirmText="确认取消"
        cancelText="继续编辑"
      />
    </div>
  );
}
