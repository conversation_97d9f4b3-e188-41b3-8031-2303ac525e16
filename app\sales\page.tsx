'use client';

import Link from "next/link";
import { useState, useEffect } from "react";

// 订单状态中英文映射
import { apiFetchJson } from '@/lib/api';
import { mapErrorMessage } from '@/lib/error-mapping';
const statusMap: { [key: string]: string } = {
  'pending': '待处理',
  'processing': '处理中',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
};

interface Order {
  id: string;
  orderNumber: string;
  customer: string;
  phone: string;
  amount: number;
  status: string;
  date: string;
}

interface Product {
  name: string;
  count: number;
  amount: number;
}

export default function SalesPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [todaySales, setTodaySales] = useState(0);
  const [todayOrderCount, setTodayOrderCount] = useState(0);
  const [averageOrderAmount, setAverageOrderAmount] = useState(0);
  const [topSellingProducts, setTopSellingProducts] = useState<Product[]>([]);
  const ordersPerPage = 5;

  // 获取订单数据
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(''); // 清除之前的错误

        // 从API获取数据
        const data = await apiFetchJson<any>(`/api/orders?page=${currentPage}&limit=${ordersPerPage}`);

        if (data.success) {
          // 设置订单数据
          setOrders(data.data?.orders || []);
          setTotalOrders(data.data?.total || 0);

          // 设置今日销售额和订单数
          setTodaySales(data.data?.todayStats?.totalSales || 0);
          setTodayOrderCount(data.data?.todayStats?.orderCount || 0);

          // 设置平均订单金额
          setAverageOrderAmount(data.data?.avgOrderAmount || 0);

          // 设置热销商品
          setTopSellingProducts(data.data?.topProducts || []);
        } else {
          // 只有在API明确返回失败时才设置错误
          console.error('API返回错误:', data.message);
          setError(data.message || '获取数据失败');

          // 确保设置空数据，避免显示旧数据
          setOrders([]);
          setTotalOrders(0);
        }
      } catch (err) {
        // 只有在网络请求或解析JSON时出错才设置错误
        console.error('获取订单数据失败:', err);
        setError('获取订单数据失败，请刷新页面重试');

        // 确保设置空数据，避免显示旧数据
        setOrders([]);
        setTotalOrders(0);
        setTodaySales(0);
        setTodayOrderCount(0);
        setAverageOrderAmount(0);
        setTopSellingProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [currentPage, ordersPerPage]);

  return (
    <div className="flex flex-col h-full gap-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">销售管理</h1>
        <div className="flex gap-3">
          <Link href="/sales/new" className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            新建销售单
          </Link>
          <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            导出数据
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">今日销售</h3>
            <span className="text-xs text-gray-500">今天</span>
          </div>
          <div className="flex items-end justify-between">
            <div>
              {loading ? (
                <div className="animate-pulse h-8 w-32 bg-gray-200 rounded"></div>
              ) : (
                <>
                  <span className="text-2xl font-bold text-blue-700">¥{todaySales.toFixed(2)}</span>
                  <div className="flex items-center text-xs text-green-600 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较昨日增长 {((todaySales > 0 ? 8.2 : 0)).toFixed(1)}%
                  </div>
                </>
              )}
            </div>
            <div className="bg-blue-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">今日订单数</h3>
            <span className="text-xs text-gray-500">今天</span>
          </div>
          <div className="flex items-end justify-between">
            <div>
              {loading ? (
                <div className="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
              ) : (
                <>
                  <span className="text-2xl font-bold text-blue-700">{todayOrderCount}</span>
                  <div className="flex items-center text-xs text-green-600 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    较昨日增长 {(todayOrderCount > 0 ? 12.5 : 0).toFixed(1)}%
                  </div>
                </>
              )}
            </div>
            <div className="bg-green-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">订单平均金额</h3>
            <span className="text-xs text-gray-500">今天</span>
          </div>
          <div className="flex items-end justify-between">
            <div>
              {loading ? (
                <div className="animate-pulse h-8 w-24 bg-gray-200 rounded"></div>
              ) : (
                <>
                  <span className="text-2xl font-bold text-blue-700">¥{averageOrderAmount.toFixed(2)}</span>
                  <div className="flex items-center text-xs text-red-600 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                    较昨日下降 {(averageOrderAmount > 0 ? 3.8 : 0).toFixed(1)}%
                  </div>
                </>
              )}
            </div>
            <div className="bg-purple-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        <div className="xl:col-span-3 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <h2 className="font-semibold text-lg text-blue-700">最近销售订单</h2>
            <div className="flex items-center">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索订单..."
                  className="px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm text-blue-700"
                />
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <select className="ml-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option>全部状态</option>
                <option>已完成</option>
                <option>待支付</option>
                <option>已取消</option>
              </select>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    订单编号
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    客户信息
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    金额
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    时间
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  // 加载状态
                  Array(5).fill(0).map((_, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                      </td>
                    </tr>
                  ))
                ) : error ? (
                  // 真正的错误状态（如网络错误、服务器错误等）
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : orders.length === 0 ? (
                  // 空数据状态
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      暂无订单数据
                    </td>
                  </tr>
                ) : (
                  // 正常数据展示
                  orders.map((order, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        <Link href={`/sales/${order.id}`}>{order.orderNumber}</Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {order.customer || '散客'} {order.phone ? `(${order.phone.substring(0, 7)}****)` : ''}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-700 font-medium">
                        ¥{order.amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {statusMap[order.status] || order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {order.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <Link href={`/sales/${order.id}`} className="text-blue-600 hover:text-blue-900 mr-3">详情</Link>
                        <button className="text-blue-600 hover:text-blue-900">打印</button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6 flex justify-between items-center">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                className={`px-4 py-2 border rounded-md text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 bg-white hover:bg-gray-50'}`}
                onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1 || loading}
              >
                上一页
              </button>
              <button
                className={`ml-3 px-4 py-2 border rounded-md text-sm font-medium ${currentPage >= Math.ceil(totalOrders / ordersPerPage) ? 'text-gray-300 cursor-not-allowed' : 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'}`}
                onClick={() => currentPage < Math.ceil(totalOrders / ordersPerPage) && setCurrentPage(currentPage + 1)}
                disabled={currentPage >= Math.ceil(totalOrders / ordersPerPage) || loading}
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              {loading ? (
                <div className="animate-pulse h-5 bg-gray-200 rounded w-64"></div>
              ) : (
                <div>
                  <p className="text-sm text-gray-700">
                    显示第 <span className="font-medium">{(currentPage - 1) * ordersPerPage + 1}</span> 到 <span className="font-medium">{Math.min(currentPage * ordersPerPage, totalOrders)}</span> 条，共 <span className="font-medium">{totalOrders}</span> 条
                  </p>
                </div>
              )}
              <div>
                <nav className="relative inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                  >
                    <span className="sr-only">上一页</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {/* 生成页码按钮 */}
                  {Array.from({ length: Math.ceil(totalOrders / ordersPerPage) || 1 }, (_, i) => i + 1)
                    .filter(page => {
                      // 显示当前页、第一页、最后一页，以及当前页附近的页码
                      const lastPage = Math.ceil(totalOrders / ordersPerPage) || 1;
                      return page === 1 || page === lastPage || Math.abs(page - currentPage) <= 1;
                    })
                    .map((page, index, array) => {
                      // 添加省略号
                      if (index > 0 && array[index - 1] !== page - 1) {
                        return (
                          <span key={`ellipsis-${page}`} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                          </span>
                        );
                      }

                      return (
                        <button
                          key={page}
                          className={`relative inline-flex items-center px-4 py-2 border ${currentPage === page ? 'border-blue-300 bg-blue-50 text-blue-700' : 'border-gray-300 bg-white text-gray-700'} text-sm font-medium hover:bg-gray-50`}
                          onClick={() => setCurrentPage(page)}
                          disabled={loading}
                        >
                          {page}
                        </button>
                      );
                    })
                  }

                  <button
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage >= Math.ceil(totalOrders / ordersPerPage) ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                    onClick={() => currentPage < Math.ceil(totalOrders / ordersPerPage) && setCurrentPage(currentPage + 1)}
                    disabled={currentPage >= Math.ceil(totalOrders / ordersPerPage) || loading}
                  >
                    <span className="sr-only">下一页</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>

        <div className="xl:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-lg text-blue-700">热销药品排行</h2>
          </div>
          <div className="p-4">
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-gray-200 mr-3"></div>
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </div>
                      <div className="text-right">
                        <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
                        <div className="h-3 bg-gray-200 rounded w-10"></div>
                      </div>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                      <div className="bg-gray-300 h-1.5 rounded-full" style={{ width: `${100 - i * 15}%` }}></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              // 真正的错误状态（如网络错误、服务器错误等）
              <div className="py-4 text-center text-red-500">{error}</div>
            ) : topSellingProducts.length === 0 ? (
              // 空数据状态
              <div className="py-4 text-center text-gray-500">暂无热销药品数据</div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {topSellingProducts.map((item, index) => (
                  <li key={index} className="py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`flex items-center justify-center w-6 h-6 rounded-full mr-3 ${index < 3 ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}>
                          {index + 1}
                        </div>
                        <span className="text-sm font-medium text-blue-700">{item.name}</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-blue-700">¥{item.amount.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{item.count}盒</p>
                      </div>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                      <div className="bg-blue-600 h-1.5 rounded-full" style={{ width: `${100 - index * 15}%` }}></div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}