'use client';

import Link from "next/link";
import { useState, useEffect } from "react";

// 订单状态中英文映射
import { apiFetchJson } from '@/lib/api';
import { mapErrorMessage } from '@/lib/error-mapping';
const statusMap: { [key: string]: string } = {
  'pending': '待处理',
  'processing': '处理中',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
};

interface Order {
  id: string;
  orderNumber: string;
  customer: string;
  phone: string;
  amount: number;
  status: string;
  date: string;
}

interface Product {
  name: string;
  count: number;
  amount: number;
}

export default function SalesPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [todaySales, setTodaySales] = useState(0);
  const [todayOrderCount, setTodayOrderCount] = useState(0);
  const [averageOrderAmount, setAverageOrderAmount] = useState(0);
  const [topSellingProducts, setTopSellingProducts] = useState<Product[]>([]);
  const ordersPerPage = 5;

  // 获取订单数据
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(''); // 清除之前的错误

        // 从API获取数据
        const data = await apiFetchJson<any>(`/api/orders?page=${currentPage}&limit=${ordersPerPage}`);

        if (data.success) {
          // 设置订单数据
          setOrders(data.data?.orders || []);
          setTotalOrders(data.data?.total || 0);

          // 设置今日销售额和订单数
          setTodaySales(data.data?.todayStats?.totalSales || 0);
          setTodayOrderCount(data.data?.todayStats?.orderCount || 0);

          // 设置平均订单金额
          setAverageOrderAmount(data.data?.avgOrderAmount || 0);

          // 设置热销商品
          setTopSellingProducts(data.data?.topProducts || []);
        } else {
          // 只有在API明确返回失败时才设置错误
          console.error('API返回错误:', data.message);
          setError(data.message || '获取数据失败');

          // 确保设置空数据，避免显示旧数据
          setOrders([]);
          setTotalOrders(0);
        }
      } catch (err) {
        // 只有在网络请求或解析JSON时出错才设置错误
        console.error('获取订单数据失败:', err);
        setError('获取订单数据失败，请刷新页面重试');

        // 确保设置空数据，避免显示旧数据
        setOrders([]);
        setTotalOrders(0);
        setTodaySales(0);
        setTodayOrderCount(0);
        setAverageOrderAmount(0);
        setTopSellingProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [currentPage, ordersPerPage]);

  return (
    <div className="flex flex-col h-full gap-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-primary">销售管理</h1>
        <div className="flex gap-3">
          <Link href="/sales/new" className="btn btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            新建销售单
          </Link>
          <button className="btn btn-outline">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            导出数据
          </button>
        </div>
      </div>

      <div className="stats stats-vertical lg:stats-horizontal shadow w-full">
        <div className="stat">
          <div className="stat-figure text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="stat-title">今日销售</div>
          {loading ? (
            <div className="stat-value">
              <div className="animate-pulse h-8 w-32 bg-gray-200 rounded"></div>
            </div>
          ) : (
            <>
              <div className="stat-value text-primary">¥{todaySales.toFixed(2)}</div>
              <div className="stat-desc text-success">
                <svg xmlns="http://www.w3.org/2000/svg" className="inline w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                较昨日增长 {((todaySales > 0 ? 8.2 : 0)).toFixed(1)}%
              </div>
            </>
          )}
        </div>

        <div className="stat">
          <div className="stat-figure text-success">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <div className="stat-title">今日订单数</div>
          {loading ? (
            <div className="stat-value">
              <div className="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
            </div>
          ) : (
            <>
              <div className="stat-value text-success">{todayOrderCount}</div>
              <div className="stat-desc text-success">
                <svg xmlns="http://www.w3.org/2000/svg" className="inline w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                较昨日增长 {(todayOrderCount > 0 ? 12.5 : 0).toFixed(1)}%
              </div>
            </>
          )}
        </div>

        <div className="stat">
          <div className="stat-figure text-accent">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div className="stat-title">订单平均金额</div>
          {loading ? (
            <div className="stat-value">
              <div className="animate-pulse h-8 w-24 bg-gray-200 rounded"></div>
            </div>
          ) : (
            <>
              <div className="stat-value text-accent">¥{averageOrderAmount.toFixed(2)}</div>
              <div className="stat-desc text-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="inline w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                </svg>
                较昨日下降 {(averageOrderAmount > 0 ? 3.8 : 0).toFixed(1)}%
              </div>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        <div className="xl:col-span-3 card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex justify-between items-center mb-4">
              <h2 className="card-title text-primary">最近销售订单</h2>
              <div className="flex items-center gap-2">
                <div className="form-control">
                  <div className="input-group">
                    <input
                      type="text"
                      placeholder="搜索订单..."
                      className="input input-bordered input-sm"
                    />
                    <button className="btn btn-square btn-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </button>
                  </div>
                </div>
                <select className="select select-bordered select-sm">
                  <option>全部状态</option>
                  <option>已完成</option>
                  <option>待支付</option>
                  <option>已取消</option>
                </select>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="table table-zebra">
                <thead>
                  <tr>
                    <th>订单编号</th>
                    <th>客户信息</th>
                    <th>金额</th>
                    <th>状态</th>
                    <th>时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    // 加载状态
                    Array(5).fill(0).map((_, index) => (
                      <tr key={index}>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </td>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                        </td>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                        </td>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                        </td>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                        </td>
                        <td>
                          <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                        </td>
                      </tr>
                    ))
                  ) : error ? (
                    // 真正的错误状态（如网络错误、服务器错误等）
                    <tr>
                      <td colSpan={6} className="text-center text-error py-8">
                        <div className="flex flex-col items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          {error}
                        </div>
                      </td>
                    </tr>
                  ) : orders.length === 0 ? (
                    // 空数据状态
                    <tr>
                      <td colSpan={6} className="text-center text-base-content/60 py-8">
                        <div className="flex flex-col items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          暂无订单数据
                        </div>
                      </td>
                    </tr>
                  ) : (
                    // 正常数据展示
                    orders.map((order, index) => (
                      <tr key={index} className="hover">
                        <td>
                          <Link href={`/sales/${order.id}`} className="link link-primary font-medium">
                            {order.orderNumber}
                          </Link>
                        </td>
                        <td>
                          <div className="flex items-center space-x-3">
                            <div className="avatar placeholder">
                              <div className="bg-neutral-focus text-neutral-content rounded-full w-8">
                                <span className="text-xs">{(order.customer || '散客').charAt(0)}</span>
                              </div>
                            </div>
                            <div>
                              <div className="font-bold">{order.customer || '散客'}</div>
                              {order.phone && (
                                <div className="text-sm opacity-50">{order.phone.substring(0, 7)}****</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td>
                          <span className="text-primary font-semibold">¥{order.amount.toFixed(2)}</span>
                        </td>
                        <td>
                          <div className="badge badge-success badge-sm">
                            {statusMap[order.status] || order.status}
                          </div>
                        </td>
                        <td>
                          <span className="text-sm opacity-50">{order.date}</span>
                        </td>
                        <td>
                          <div className="flex gap-2">
                            <Link href={`/sales/${order.id}`} className="btn btn-ghost btn-xs">详情</Link>
                            <button className="btn btn-ghost btn-xs">打印</button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            <div className="flex justify-between items-center mt-4">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  className={`btn btn-sm ${currentPage === 1 ? 'btn-disabled' : 'btn-outline'}`}
                  onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                >
                  上一页
                </button>
                <button
                  className={`btn btn-sm ${currentPage >= Math.ceil(totalOrders / ordersPerPage) ? 'btn-disabled' : 'btn-primary'}`}
                  onClick={() => currentPage < Math.ceil(totalOrders / ordersPerPage) && setCurrentPage(currentPage + 1)}
                  disabled={currentPage >= Math.ceil(totalOrders / ordersPerPage) || loading}
                >
                  下一页
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                {loading ? (
                  <div className="animate-pulse h-5 bg-gray-200 rounded w-64"></div>
                ) : (
                  <div>
                    <p className="text-sm">
                      显示第 <span className="font-medium text-primary">{(currentPage - 1) * ordersPerPage + 1}</span> 到 <span className="font-medium text-primary">{Math.min(currentPage * ordersPerPage, totalOrders)}</span> 条，共 <span className="font-medium text-primary">{totalOrders}</span> 条
                    </p>
                  </div>
                )}
                <div className="btn-group">
                  <button
                    className={`btn btn-sm ${currentPage === 1 ? 'btn-disabled' : 'btn-outline'}`}
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                  >
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {/* 生成页码按钮 */}
                  {Array.from({ length: Math.ceil(totalOrders / ordersPerPage) || 1 }, (_, i) => i + 1)
                    .filter(page => {
                      // 显示当前页、第一页、最后一页，以及当前页附近的页码
                      const lastPage = Math.ceil(totalOrders / ordersPerPage) || 1;
                      return page === 1 || page === lastPage || Math.abs(page - currentPage) <= 1;
                    })
                    .map((page, index, array) => {
                      // 添加省略号
                      if (index > 0 && array[index - 1] !== page - 1) {
                        return (
                          <span key={`ellipsis-${page}`} className="btn btn-sm btn-disabled">
                            ...
                          </span>
                        );
                      }

                      return (
                        <button
                          key={page}
                          className={`btn btn-sm ${currentPage === page ? 'btn-primary' : 'btn-outline'}`}
                          onClick={() => setCurrentPage(page)}
                          disabled={loading}
                        >
                          {page}
                        </button>
                      );
                    })
                  }

                  <button
                    className={`btn btn-sm ${currentPage >= Math.ceil(totalOrders / ordersPerPage) ? 'btn-disabled' : 'btn-outline'}`}
                    onClick={() => currentPage < Math.ceil(totalOrders / ordersPerPage) && setCurrentPage(currentPage + 1)}
                    disabled={currentPage >= Math.ceil(totalOrders / ordersPerPage) || loading}
                  >
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="xl:col-span-2 card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title text-primary">热销药品排行</h2>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center space-x-4 animate-pulse">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gray-300 h-2 rounded-full" style={{ width: `${100 - i * 15}%` }}></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="h-4 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              // 真正的错误状态（如网络错误、服务器错误等）
              <div className="flex flex-col items-center py-8 text-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                {error}
              </div>
            ) : topSellingProducts.length === 0 ? (
              // 空数据状态
              <div className="flex flex-col items-center py-8 text-base-content/60">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                暂无热销药品数据
              </div>
            ) : (
              <div className="space-y-4">
                {topSellingProducts.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className={`avatar placeholder ${index < 3 ? 'bg-primary text-primary-content' : 'bg-neutral text-neutral-content'}`}>
                        <div className="w-8 rounded-full">
                          <span className="text-xs">{index + 1}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium text-primary truncate">{item.name}</p>
                        <div className="text-right ml-2">
                          <p className="text-sm font-semibold text-primary">¥{item.amount.toLocaleString()}</p>
                          <p className="text-xs opacity-50">{item.count}盒</p>
                        </div>
                      </div>
                      <div className="w-full bg-base-300 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${index < 3 ? 'bg-primary' : 'bg-neutral'}`}
                          style={{ width: `${100 - index * 15}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}