import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('@/lib/db', async () => ({ query: vi.fn(), run: vi.fn() }));
const { run, query } = vi.mocked(await import('@/lib/db')) as any;

async function loadStockInV2() { return await import('../../app/api/inventory/stock-in-v2/route'); }
async function loadStockOutV2() { return await import('../../app/api/inventory/stock-out-v2/route'); }

describe('inventory errorCode 基础校验', () => {
  beforeEach(() => { vi.resetAllMocks(); });

  it('stock-in-v2 缺参 -> BAD_REQUEST', async () => {
    const { POST } = await loadStockInV2();
    const req: any = { json: async () => ({ quantity: 1, stock_in_date: '2025-01-01' }) };
    const res: any = await POST(req);
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });

  it('stock-out-v2 缺参 -> BAD_REQUEST', async () => {
    const { POST } = await loadStockOutV2();
    const req: any = { json: async () => ({ product_id: 1, stock_out_date: '2025-01-01' }) };
    const res: any = await POST(req);
    const body = await res.json();
    expect(res.status).toBe(400);
    expect(body.errorCode).toBe('BAD_REQUEST');
  });
});

