# 测试框架配置（Vitest）

## 技术选型
- 选择 Vitest 的原因：
  - 与 Vite/现代前端生态兼容性好、启动快、体验轻量
  - TypeScript 友好，mock 能力强，易于对 Next.js API Route 进行单测
  - 与 Jest 用法接近，迁移成本低

## 安装与依赖
- 已通过包管理器安装（见 package.json）：
  - devDependencies: `vitest`, `@vitest/coverage-v8`
- 如需安装：
  - `npm install -D vitest @vitest/coverage-v8`

## 脚本命令
- 一次性运行：`npm run test`
- 监听运行：`npm run test:watch`

## 基本用法示例
- 测试文件命名：`tests/**/*.spec.ts` 或 `tests/**/*.test.ts`
- 示例：`tests/api/sales/scan-trace-code.spec.ts`
  - 通过 `vi.mock('@/lib/db')` mock 数据库访问
  - 通过 `global.fetch = vi.fn()` mock 对外部平台的 API 调用
  - 动态导入 API Route 模块以执行 `POST` 方法

## 覆盖率（可选）
- 可启用覆盖率：`vitest --coverage`（已安装 @vitest/coverage-v8）

## 注意事项
- 遵循 docs 目录的开发规范，测试命名与描述使用中文，便于团队理解
- 测试不应依赖真实外部平台或真实数据库，使用 mock 断言边界与分支

