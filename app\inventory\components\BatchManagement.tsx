'use client';

import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import ErrorModal from '../../components/ErrorModal';
import { apiFetchJson } from '@/lib/api';

interface Batch {
  id: number;
  product_id: number;
  batch_number: string;
  production_date: string;
  expiry_date: string;
  quantity: number;
  remaining_quantity?: number;
  supplier_name: string;
  purchase_price: number;
  status: 'active' | 'expired' | 'depleted';
}

interface Product {
  id: number;
  name: string;
  dosage_form: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

interface BatchManagementProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: number; // 可选，如果提供则只显示指定产品的批次
}

export default function BatchManagement({ isOpen, onClose, productId }: BatchManagementProps) {
  const [batches, setBatches] = useState<Batch[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProductId, setSelectedProductId] = useState<number | undefined>(productId);
  const [expandedBatchId, setExpandedBatchId] = useState<number | null>(null);
  const [confirmMessage, setConfirmMessage] = useState<string | null>(null);
  const [batchToExpire, setBatchToExpire] = useState<number | null>(null);
  const [errorModal, setErrorModal] = useState<{show: boolean, message: string, details?: string}>({
    show: false,
    message: '',
    details: ''
  });

  useEffect(() => {
    if (isOpen) {
      const initializeData = async () => {
        setLoading(true);
        try {
          await fetchProducts();
          // 如果传入了productId，直接设置并加载该药品的批次
          if (productId) {
            setSelectedProductId(productId);
            await fetchBatches(productId);
          } else if (selectedProductId) {
            await fetchBatches(selectedProductId);
          }
        } catch (error) {
          console.error('初始化数据失败:', error);
        } finally {
          setLoading(false);
        }
      };

      initializeData();
    }
  }, [isOpen, productId]);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json() as ApiResponse<Product[]>;
      if (data.success && data.data) {
        setProducts(data.data);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
    }
  };

  const fetchBatches = async (pid?: number) => {
    if (!pid) return;

    try {
      const data = await apiFetchJson<ApiResponse<Batch[]>>(`/api/inventory/batches?product_id=${pid}`);
      if (data.success && data.data) {
        setBatches(data.data);
      }
    } catch (error) {
      console.error('获取批次数据失败:', error);
      throw error; // 重新抛出错误，让外部处理
    }
  };

  const handleProductChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const pid = parseInt(e.target.value);
    setSelectedProductId(pid);

    if (pid) {
      setLoading(true);
      try {
        await fetchBatches(pid);
      } catch (error) {
        console.error('获取批次数据失败:', error);
      } finally {
        setLoading(false);
      }
    } else {
      setBatches([]);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
  };

  const getStatusBadge = (status: string, expiryDate: string) => {
    // 检查是否即将过期 (30天内)
    const today = new Date();
    const expiry = new Date(expiryDate);
    const daysUntilExpiry = Math.floor((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (status === 'expired') {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已过期</span>;
    } else if (status === 'depleted') {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已用完</span>;
    } else if (daysUntilExpiry <= 0) {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已过期</span>;
    } else if (daysUntilExpiry <= 30) {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">即将过期 ({daysUntilExpiry}天)</span>;
    } else {
      return <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span>;
    }
  };

  const confirmMarkAsExpired = (batchId: number) => {
    setBatchToExpire(batchId);
    setConfirmMessage('确定标记此批次为过期/作废吗？这将从可用库存中移除该批次。');
  };

  const handleConfirmExpire = async () => {
    if (batchToExpire) {
      try {
        const response = await fetch(`/api/inventory/batches/${batchToExpire}/expire`, {
          method: 'POST',
        });
        const data = await response.json();
        if (data.success) {
          // 重新加载批次数据
          if (selectedProductId) {
            fetchBatches(selectedProductId);
          }
        }
      } catch (error) {
        console.error('标记批次为过期时出错:', error);
      } finally {
        setConfirmMessage(null);
        setBatchToExpire(null);
      }
    }
  };

  return (
    <>
      <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        aria-hidden="true"
      />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-4xl w-full bg-gray-50 rounded-xl shadow-lg border border-gray-200 max-h-[90vh] flex flex-col">
          <div className="p-6 border-b border-gray-200">
            <Dialog.Title className="text-xl font-medium leading-6 text-blue-700">
              批次管理
            </Dialog.Title>
          </div>

          <div className="p-6 flex-grow overflow-auto">
            {confirmMessage && (
              <div className="mb-4 p-4 bg-yellow-50 border border-yellow-300 rounded-md">
                <p className="text-yellow-800 mb-2">{confirmMessage}</p>
                <div className="flex justify-end space-x-2">
                  <button
                    className="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300"
                    onClick={() => {
                      setConfirmMessage(null);
                      setBatchToExpire(null);
                    }}
                  >
                    取消
                  </button>
                  <button
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                    onClick={handleConfirmExpire}
                  >
                    确认作废
                  </button>
                </div>
              </div>
            )}

            <div className="mb-6">
              <label className="block text-sm font-medium text-black mb-1">
                {productId ? '当前药品' : '选择药品'}
              </label>
              {productId ? (
                // 如果传入了productId，显示当前选中的药品信息
                <div className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-100 px-3 py-2 text-black">
                  {products.find(p => p.id === productId)?.name || '加载中...'}
                  {products.find(p => p.id === productId)?.dosage_form &&
                    ` (${products.find(p => p.id === productId)?.dosage_form})`}
                </div>
              ) : (
                // 如果没有传入productId，显示选择下拉框
                <select
                  value={selectedProductId || ''}
                  onChange={handleProductChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-black"
                >
                  <option value="">请选择药品</option>
                  {products.map(product => (
                    <option key={product.id} value={product.id}>
                      {product.name} ({product.dosage_form})
                    </option>
                  ))}
                </select>
              )}
            </div>

            {loading ? (
              <div className="flex justify-center my-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : batches.length > 0 ? (
              <div className="overflow-hidden rounded-lg border border-gray-200">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">批次号</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生产日期</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期至</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余数量</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {batches.map(batch => (
                      <React.Fragment key={batch.id}>
                        <tr
                          className={`hover:bg-blue-50 cursor-pointer ${expandedBatchId === batch.id ? 'bg-blue-50' : ''}`}
                          onClick={() => setExpandedBatchId(expandedBatchId === batch.id ? null : batch.id)}
                        >
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">{batch.batch_number}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{formatDate(batch.production_date)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{formatDate(batch.expiry_date)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">{batch.remaining_quantity || batch.quantity}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{batch.supplier_name || '-'}</td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {getStatusBadge(batch.status, batch.expiry_date)}
                          </td>
                        </tr>
                        {expandedBatchId === batch.id && (
                          <tr className="bg-blue-50">
                            <td colSpan={6} className="px-4 py-4">
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="font-medium text-gray-700">进货价格:</span>
                                  <span className="ml-2 text-gray-900">¥{batch.purchase_price.toFixed(2)}</span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">批次状态:</span>
                                  <span className="ml-2 text-gray-900">
                                    {batch.status === 'active' ? '活跃' :
                                     batch.status === 'expired' ? '已过期' :
                                     batch.status === 'depleted' ? '已用完' : '未知'}
                                  </span>
                                </div>
                                <div className="col-span-2 mt-2">
                                  <button
                                    className="px-3 py-1 text-xs font-medium text-white bg-red-500 rounded hover:bg-red-600"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      confirmMarkAsExpired(batch.id);
                                    }}
                                  >
                                    标记为过期/作废
                                  </button>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : selectedProductId ? (
              <div className="text-center py-8 text-gray-500">
                此药品暂无批次记录
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                请选择一个药品来查看批次信息
              </div>
            )}
          </div>

          <div className="px-6 py-4 bg-gray-100 rounded-b-xl flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              关闭
            </button>
          </div>
        </Dialog.Panel>
      </div>
      </Dialog>

      {/* 错误提示模态框 */}
      <ErrorModal
        isOpen={errorModal.show}
        onClose={() => setErrorModal({show: false, message: '', details: ''})}
        message={errorModal.message}
        details={errorModal.details}
      />
    </>
  );
}