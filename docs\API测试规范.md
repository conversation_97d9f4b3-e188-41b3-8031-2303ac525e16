# API 测试规范

## 目标接口
- `/api/sales/scan-trace-code`：销售管理中扫描追溯码
- `/api/products/barcode`：按条码查询药品
- `/api/suppliers`：供应商增删改查
- `/api/inventory/stock-in(-v2)`：库存入库
- `/api/inventory/stock-out(-v2)`：库存出库
- `/api/orders` 与 `/api/orders/[id]`：订单创建、查询与修改

## 测试范围（通用）
- 参数校验：缺少必要参数返回 BAD_REQUEST
- 资源不存在：返回 NOT_FOUND
- 冲突类错误：返回 CONFLICT（409）
- 业务校验：如库存不足 OUT_OF_STOCK，返回 data.currentStock
- 上游错误透传：UPSTREAM_ERROR / ENT_ID_MISSING
- 内部错误：INTERNAL_ERROR

## 用例清单（示例）
- suppliers
  - POST 缺名称 -> 400 BAD_REQUEST
  - POST 名称重复 -> 409 CONFLICT
  - DELETE 缺 id -> 400 BAD_REQUEST
- inventory
  - stock-in-v2 缺参 -> 400 BAD_REQUEST
  - stock-out-v2 缺参 -> 400 BAD_REQUEST
- sales/scan-trace-code（详见原节）

## 执行方法
- 自动化：`npm run test`
- 手工/集成：`tests/*.md` 文件提供手工步骤

## 期望输出
- 各接口错误分支具有稳定的 errorCode 值

## 最佳实践
- 前端基于 errorCode 进行分支处理，message 仅作为展示
- 保持 docs/API错误码规范.md 中定义的一致性

