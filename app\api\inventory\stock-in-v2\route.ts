import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

interface StockInRequest {
  product_id: number;
  quantity: number;
  batch_number?: string;
  auto_generate_batch?: boolean;
  batch_type?: string;
  supplier_id?: number;
  cost_price?: number;
  production_date?: string;
  expiry_date?: string;
  stock_in_date: string;
  notes?: string;
  trace_codes?: string[];
  upload_to_mashangfangxin?: boolean;
  operator?: string;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockInRequest;
    const {
      product_id,
      quantity,
      batch_number,
      auto_generate_batch,
      batch_type,
      supplier_id,
      cost_price,
      production_date,
      expiry_date,
      stock_in_date,
      notes,
      trace_codes,
      upload_to_mashangfangxin,
      operator
    } = body;

    // 验证数据
    if (!product_id || !quantity || !stock_in_date) {
      return NextResponse.json({
        success: false,
        errorCode: 'BAD_REQUEST',
        message: '缺少必要参数：product_id, quantity, stock_in_date'
      }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({
        success: false,
        errorCode: 'BAD_REQUEST',
        message: '入库数量必须大于0'
      }, { status: 400 });
    }

    // 验证日期格式
    if (expiry_date && isNaN(Date.parse(expiry_date))) {
      return NextResponse.json({
        success: false,
        errorCode: 'BAD_REQUEST',
        message: '有效期格式不正确'
      }, { status: 400 });
    }

    if (isNaN(Date.parse(stock_in_date))) {
      return NextResponse.json({
        success: false,
        message: '入库日期格式不正确'
      }, { status: 400 });
    }

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 1. 检查产品是否存在
      const product = await query('SELECT * FROM 药品信息 WHERE 编号 = ?', [product_id]);
      if (!product || product.length === 0) {
        throw new Error('产品不存在');
      }

      // 2. 确保药品库存记录存在
      let stockRecord = await query('SELECT * FROM 药品库存 WHERE 药品编号 = ?', [product_id]);
      if (!stockRecord || stockRecord.length === 0) {
        await run(`
          INSERT INTO 药品库存 (药品编号, 当前库存, 最低库存, 最高库存, 创建时间, 更新时间)
          VALUES (?, 0, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [product_id]);
        
        stockRecord = await query('SELECT * FROM 药品库存 WHERE 药品编号 = ?', [product_id]);
      }

      const currentStock = stockRecord[0].当前库存 || 0;

      // 3. 生成或验证批次号
      let finalBatchNumber = batch_number;
      if (auto_generate_batch && !batch_number) {
        const batchPrefix = batch_type || 'RK';
        const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        
        // 查询今天的最大序号
        const maxSeq = await query(
          `SELECT MAX(CAST(SUBSTR(批次号, -4) AS INTEGER)) as max_seq 
           FROM 药品批次表 
           WHERE 批次号 LIKE ? AND 药品编号 = ?`,
          [`${batchPrefix}-${today}%`, product_id]
        );
        
        const nextSeq = (maxSeq[0]?.max_seq || 0) + 1;
        finalBatchNumber = `${batchPrefix}-${today}${nextSeq.toString().padStart(4, '0')}`;
      }

      if (!finalBatchNumber) {
        throw new Error('批次号不能为空');
      }

      // 4. 检查批次是否已存在
      const existingBatch = await query(
        'SELECT * FROM 药品批次表 WHERE 药品编号 = ? AND 批次号 = ?',
        [product_id, finalBatchNumber]
      );

      let batchId;
      if (existingBatch && existingBatch.length > 0) {
        // 更新现有批次
        batchId = existingBatch[0].编号;
        const newBatchQuantity = existingBatch[0].当前数量 + quantity;
        const newInboundQuantity = existingBatch[0].入库数量 + quantity;
        
        await run(`
          UPDATE 药品批次表 
          SET 当前数量 = ?, 入库数量 = ?, 更新时间 = CURRENT_TIMESTAMP
          WHERE 编号 = ?
        `, [newBatchQuantity, newInboundQuantity, batchId]);
      } else {
        // 创建新批次
        const batchResult = await run(`
          INSERT INTO 药品批次表 (
            药品编号, 批次号, 生产日期, 有效期, 入库数量, 当前数量,
            供应商编号, 入库成本价, 状态, 创建时间, 更新时间, 备注
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?)
        `, [
          product_id,
          finalBatchNumber,
          production_date || null,
          expiry_date || null,
          quantity,
          quantity,
          supplier_id || null,
          cost_price || null,
          notes || null
        ]);
        
        batchId = batchResult.lastID;
      }

      // 5. 更新药品库存汇总
      const newStock = currentStock + quantity;
      await run(`
        UPDATE 药品库存 
        SET 当前库存 = ?, 最后入库时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
        WHERE 药品编号 = ?
      `, [newStock, product_id]);

      // 6. 记录库存变动
      await run(`
        INSERT INTO 库存变动记录 (
          药品编号, 批次编号, 操作类型, 变动数量, 变动前数量, 变动后数量,
          单价, 总金额, 供应商编号, 关联单据类型, 关联单据编号,
          操作人, 操作时间, 备注
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        product_id,
        batchId,
        '入库',
        quantity,
        currentStock,
        newStock,
        cost_price || null,
        cost_price ? (cost_price * quantity) : null,
        supplier_id || null,
        '采购单',
        null, // 关联单据编号，实际应用中应该传入
        operator || '系统操作员',
        stock_in_date,
        notes || null
      ]);

      // 7. 处理追溯码（如果有）
      if (trace_codes && trace_codes.length > 0) {
        // 这里可以添加追溯码处理逻辑
        console.log('处理追溯码:', trace_codes);
      }

      // 8. 上传到码上放心平台（如果需要）
      if (upload_to_mashangfangxin) {
        // 这里可以添加码上放心平台上传逻辑
        console.log('上传到码上放心平台');
      }

      // 提交事务
      await run('COMMIT');

      // 获取更新后的库存信息
      const updatedStock = await query(`
        SELECT 
          s.当前库存 as current_stock,
          s.最低库存 as min_stock,
          s.最高库存 as max_stock
        FROM 药品库存 s
        WHERE s.药品编号 = ?
      `, [product_id]);

      return NextResponse.json({
        success: true,
        message: '入库成功',
        data: {
          batch_number: finalBatchNumber,
          batch_id: batchId,
          quantity_added: quantity,
          new_stock: newStock,
          stock_info: updatedStock[0] || {}
        }
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('入库操作失败:', error);
    return NextResponse.json(
      {
        success: false,
        errorCode: 'INTERNAL_ERROR',
        message: '入库操作失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取入库历史记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('product_id');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    let whereClause = "WHERE r.操作类型 = '入库'";
    let params = [];

    if (productId) {
      whereClause += " AND r.药品编号 = ?";
      params.push(productId);
    }

    const records = await query(`
      SELECT 
        r.编号 as id,
        r.药品编号 as product_id,
        p.名称 as product_name,
        r.批次编号 as batch_id,
        b.批次号 as batch_number,
        r.变动数量 as quantity,
        r.变动前数量 as before_quantity,
        r.变动后数量 as after_quantity,
        r.单价 as unit_price,
        r.总金额 as total_amount,
        r.供应商编号 as supplier_id,
        s.名称 as supplier_name,
        r.操作人 as operator,
        r.操作时间 as operation_time,
        r.备注 as notes
      FROM 库存变动记录 r
      JOIN 药品信息 p ON r.药品编号 = p.编号
      LEFT JOIN 药品批次表 b ON r.批次编号 = b.编号
      LEFT JOIN 供应商 s ON r.供应商编号 = s.编号
      ${whereClause}
      ORDER BY r.操作时间 DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    return NextResponse.json({
      success: true,
      data: records || []
    });

  } catch (error) {
    console.error('获取入库记录失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取入库记录失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
