/**
 * 批次号管理工具函数
 */

/**
 * 验证批次号格式
 * @param batchNumber 批次号
 * @returns 验证结果
 */
export function validateBatchNumber(batchNumber: string): { isValid: boolean; message?: string } {
  if (!batchNumber || batchNumber.trim().length === 0) {
    return { isValid: false, message: '批次号不能为空' };
  }

  const trimmedBatchNumber = batchNumber.trim();

  if (trimmedBatchNumber.length > 50) {
    return { isValid: false, message: '批次号长度不能超过50个字符' };
  }

  // 允许字母、数字、连字符、下划线和中文字符
  if (!/^[A-Za-z0-9\-_\u4e00-\u9fa5]+$/.test(trimmedBatchNumber)) {
    return { isValid: false, message: '批次号只能包含字母、数字、连字符、下划线和中文字符' };
  }

  // 检查是否包含危险字符
  const dangerousChars = ['<', '>', '"', "'", '&', 'script', 'SELECT', 'INSERT', 'UPDATE', 'DELETE'];
  const upperBatchNumber = trimmedBatchNumber.toUpperCase();
  for (const char of dangerousChars) {
    if (upperBatchNumber.includes(char.toUpperCase())) {
      return { isValid: false, message: '批次号包含不允许的字符' };
    }
  }

  return { isValid: true };
}

/**
 * 从药品追溯码API响应中提取批次号
 * @param apiResponse 码上放心API响应
 * @returns 批次号信息
 */
export function extractBatchNumberFromTraceCode(apiResponse: any): {
  batchNumber?: string;
  expiryDate?: string;
  produceDate?: string;
} {
  try {
    const codeInfo = apiResponse?.result?.models?.code_full_info_dto?.[0];
    if (!codeInfo) {
      return {};
    }

    const produceInfo = codeInfo.code_produce_info_d_t_o?.produce_info_list?.produce_info_dto?.[0];
    if (!produceInfo) {
      return {};
    }

    return {
      batchNumber: produceInfo.batch_no,
      expiryDate: produceInfo.expire_date,
      produceDate: produceInfo.produce_date_str
    };
  } catch (error) {
    console.error('提取批次号信息失败:', error);
    return {};
  }
}

/**
 * 生成系统批次号（仅用于测试或特殊情况）
 * @param prefix 前缀，默认为 'BATCH'
 * @returns 生成的批次号
 */
export function generateSystemBatchNumber(prefix: string = 'BATCH'): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
  const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `${prefix}-${dateStr}-${randomStr}`;
}

/**
 * 通过API生成批次号
 * @param batchType 批次类型
 * @param productId 药品ID
 * @returns 生成的批次号信息
 */
export async function generateBatchNumberViaAPI(
  batchType: string = 'PC',
  productId?: number
): Promise<{
  success: boolean;
  batchNumber?: string;
  data?: any;
  error?: string;
}> {
  try {
    // 构建URL，兼容服务器端渲染
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const url = `/api/inventory/batch-number?type=${batchType}${productId ? `&product_id=${productId}` : ''}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.success) {
      return {
        success: true,
        batchNumber: data.data.batchNumber,
        data: data.data
      };
    } else {
      return {
        success: false,
        error: data.message || '生成批次号失败'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误'
    };
  }
}

/**
 * 验证批次号（通过API）
 * @param batchNumber 批次号
 * @param productId 药品ID
 * @returns 验证结果
 */
export async function validateBatchNumberViaAPI(
  batchNumber: string,
  productId?: number
): Promise<{
  success: boolean;
  isValid?: boolean;
  isDuplicate?: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const { apiPostJson } = await import('./api');
    const data = await apiPostJson<any>('/api/inventory/batch-number', { batchNumber, productId });

    if (data.success) {
      return {
        success: true,
        isValid: data.data.isValid,
        isDuplicate: data.data.isDuplicate,
        data: data.data
      };
    } else {
      return {
        success: false,
        error: data.message || '验证批次号失败'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误'
    };
  }
}

/**
 * 格式化批次号显示
 * @param batchNumber 批次号
 * @param source 来源：'trace_code' | 'manual' | 'system'
 * @returns 格式化后的显示信息
 */
export function formatBatchNumberDisplay(batchNumber: string, source: 'trace_code' | 'manual' | 'system'): {
  displayText: string;
  sourceText: string;
  sourceIcon: string;
} {
  const sourceMap = {
    trace_code: { text: '追溯码解析', icon: '🔍' },
    manual: { text: '手动输入', icon: '✏️' },
    system: { text: '系统生成', icon: '🤖' }
  };

  const sourceInfo = sourceMap[source];

  return {
    displayText: batchNumber,
    sourceText: sourceInfo.text,
    sourceIcon: sourceInfo.icon
  };
}

/**
 * 检查批次号是否已存在
 * @param batchNumber 批次号
 * @param productId 药品ID
 * @param excludeInventoryId 排除的库存记录ID（用于更新时排除自身）
 * @returns 是否存在
 */
export async function checkBatchNumberExists(
  batchNumber: string, 
  productId: number, 
  excludeInventoryId?: number
): Promise<boolean> {
  // 这里需要导入数据库查询函数
  // 由于循环依赖问题，这个函数应该在使用时再实现具体的数据库查询逻辑
  return false;
}

/**
 * 批次号建议列表（基于历史数据）
 * @param productId 药品ID
 * @param limit 返回数量限制
 * @returns 批次号建议列表
 */
export async function getBatchNumberSuggestions(
  productId: number, 
  limit: number = 10
): Promise<string[]> {
  // 这里需要导入数据库查询函数
  // 由于循环依赖问题，这个函数应该在使用时再实现具体的数据库查询逻辑
  return [];
}

/**
 * 批次号数据清理（移除前后空格，统一格式）
 * @param batchNumber 原始批次号
 * @returns 清理后的批次号
 */
export function cleanBatchNumber(batchNumber: string): string {
  if (!batchNumber) return '';
  
  return batchNumber
    .trim()
    .replace(/\s+/g, '') // 移除所有空格
    .toUpperCase(); // 统一转为大写（可选，根据业务需求调整）
}

/**
 * 批次号匹配度计算（用于模糊搜索）
 * @param input 输入的批次号
 * @param target 目标批次号
 * @returns 匹配度分数 (0-1)
 */
export function calculateBatchNumberSimilarity(input: string, target: string): number {
  if (!input || !target) return 0;
  
  const cleanInput = cleanBatchNumber(input);
  const cleanTarget = cleanBatchNumber(target);
  
  if (cleanInput === cleanTarget) return 1;
  
  // 简单的字符串相似度计算
  const maxLength = Math.max(cleanInput.length, cleanTarget.length);
  const distance = levenshteinDistance(cleanInput, cleanTarget);
  
  return 1 - (distance / maxLength);
}

/**
 * 计算两个字符串的编辑距离
 * @param str1 字符串1
 * @param str2 字符串2
 * @returns 编辑距离
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}
