@import "tailwindcss";
@plugin "daisyui";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* DaisyUI主题配置 - 保持蓝色主题一致性 */
  --color-primary: #1d4ed8; /* blue-700 */
  --color-primary-content: #ffffff;
  --color-secondary: #64748b; /* slate-500 */
  --color-secondary-content: #ffffff;
  --color-accent: #0ea5e9; /* sky-500 */
  --color-accent-content: #ffffff;
  --color-neutral: #374151; /* gray-700 */
  --color-neutral-content: #ffffff;
  --color-base-100: #ffffff;
  --color-base-200: #f8fafc; /* slate-50 */
  --color-base-300: #e2e8f0; /* slate-200 */
  --color-base-content: #1f2937; /* gray-800 */
  --color-info: #0ea5e9; /* sky-500 */
  --color-info-content: #ffffff;
  --color-success: #10b981; /* emerald-500 */
  --color-success-content: #ffffff;
  --color-warning: #f59e0b; /* amber-500 */
  --color-warning-content: #ffffff;
  --color-error: #ef4444; /* red-500 */
  --color-error-content: #ffffff;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
