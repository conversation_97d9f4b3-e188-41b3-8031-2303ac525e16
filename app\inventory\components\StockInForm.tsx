'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { api<PERSON><PERSON>ch<PERSON>son, apiPostJson } from '@/lib/api';
// 移除SimpleBatchNumberInput导入，改为自动生成

interface Product {
  id: number;
  name: string;
  generic_name: string;
  drug_identification_code: string;
  stock_quantity: number;
  category_id: number;
  category_name: string;
  manufacturer: string;
  specification: string;
  supplier_id?: number;
  supplier_name?: string;
}

interface Category {
  id: number;
  name: string;
}

interface Supplier {
  id: number;
  name: string;
}

interface StockInFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

// 单个药品入库项目
interface StockInItem {
  id: string; // 临时ID，用于列表管理
  product_id: number;
  product_name: string;
  product_specification?: string;
  quantity: number;
  supplier_id: number;
  supplier_name?: string;
  batch_number: string;
  auto_generate_batch: boolean;
  batch_type: string;
  production_date: string; // 生产日期
  expiry_date: string;
  cost_price: number;
  trace_codes: string[];
  notes: string;
}

// 批量入库表单数据
interface BatchStockInFormData {
  stock_in_date: string;
  items: StockInItem[];
  upload_to_mashangfangxin: boolean;
  global_notes: string;
}

// 追溯码扫描结果接口
interface TraceCodeScanResult {
  success: boolean;
  drugInfo?: any;
  codeType?: string;
  message?: string;
}

// 添加药品模式
type AddProductMode = 'search' | 'scan';

export default function StockInForm({ isOpen, onClose, onSubmit }: StockInFormProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  // 批量入库相关状态
  const [batchFormData, setBatchFormData] = useState<BatchStockInFormData>({
    stock_in_date: new Date().toISOString().split('T')[0],
    items: [],
    upload_to_mashangfangxin: false,
    global_notes: ''
  });

	  // 单个药品编辑表单（用于手动添加/编辑时）
	  const [formData, setFormData] = useState({
	    product_id: 0,
	    supplier_id: 0,
	    auto_generate_batch: true,
	    batch_type: 'PC',
	    batch_number: '',
	    production_date: '',
	    expiry_date: '',
	    quantity: 1,
	    cost_price: 0,
	    trace_codes: [] as string[],
	    notes: ''
	  });

  // 添加药品相关状态
  const [addMode, setAddMode] = useState<AddProductMode>('search');
  const [searchMethod, setSearchMethod] = useState<'category' | 'barcode' | 'name'>('name');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number>(0);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // 扫码相关状态
  const [traceCode, setTraceCode] = useState('');
  const [scanResult, setScanResult] = useState<TraceCodeScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scannedDrugInfo, setScannedDrugInfo] = useState<any>(null);

  // 当前编辑的药品项目
  const [editingItem, setEditingItem] = useState<StockInItem | null>(null);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [expandedBatchItems, setExpandedBatchItems] = useState<Set<string>>(new Set());

  // 通知系统
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    show: boolean;
  }>({
    message: '',
    type: 'info',
    show: false
  });

  // 显示通知
  const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setNotification({ message, type, show: true });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };



  useEffect(() => {
    fetchProducts();
    fetchSuppliers();
    fetchCategories();
  }, []);

  // 药品搜索和筛选
  useEffect(() => {
    let filtered = products;

    if (searchMethod === 'category' && selectedCategory > 0) {
      filtered = products.filter(product => product.category_id === selectedCategory);
    } else if (searchMethod === 'barcode' && searchTerm.trim()) {
      filtered = products.filter(product =>
        product.drug_identification_code && product.drug_identification_code.includes(searchTerm.trim())
      );
    } else if (searchMethod === 'name' && searchTerm.trim()) {
      const term = searchTerm.trim().toLowerCase();
      filtered = products.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.generic_name?.toLowerCase().includes(term) ||
        product.manufacturer?.toLowerCase().includes(term)
      );
    }

    setFilteredProducts(filtered);
  }, [products, searchMethod, searchTerm, selectedCategory]);

  // 重置表单
  useEffect(() => {
    if (isOpen) {
      setBatchFormData({
        stock_in_date: new Date().toISOString().split('T')[0],
        items: [],
        upload_to_mashangfangxin: false,
        global_notes: ''
      });
      setAddMode('search');
      setTraceCode('');
      setScanResult(null);
      setScannedDrugInfo(null);
      setSearchMethod('name');
      setSearchTerm('');
      setSelectedCategory(0);
      setSelectedProduct(null);
      setEditingItem(null);
      setIsAddingItem(false);
    }
  }, [isOpen]);

  const fetchProducts = async () => {
    try {
      const data = await apiFetchJson<any>('/api/products');
      if (data.success) {
        setProducts(data.data);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const data = await apiFetchJson<any>('/api/suppliers');
      if (data.success) {
        setSuppliers(data.data);
      }
    } catch (error) {
      console.error('获取供应商数据失败:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await apiFetchJson<any>('/api/categories');
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('获取药品分类数据失败:', error);
    }
  };

  // 处理搜索方式变化
  const handleSearchMethodChange = (method: 'category' | 'barcode' | 'name') => {
    setSearchMethod(method);
    setSearchTerm('');
    setSelectedCategory(0);
    setSelectedProduct(null);
    setFormData(prev => ({ ...prev, product_id: 0 }));
  };

  // 处理药品选择
  const handleProductSelect = async (productId: number) => {
    const product = filteredProducts.find(p => p.id === productId);
    setSelectedProduct(product || null);
    setFormData(prev => ({
      ...prev,
      product_id: productId,
      supplier_id: product?.supplier_id || 0
    }));

    // 自动生成批次号
    if (productId > 0 && formData.auto_generate_batch) {
      const batchNumber = await generateBatchNumber(formData.batch_type, productId);
      setFormData(prev => ({
        ...prev,
        batch_number: batchNumber
      }));
    }
  };

  // 处理条形码扫描
  const handleBarcodeSearch = async (barcode: string) => {
    const product = products.find(p => p.drug_identification_code === barcode);
    if (product) {
      setSelectedProduct(product);
      setFormData(prev => ({
        ...prev,
        product_id: product.id,
        supplier_id: product.supplier_id || 0
      }));
      setSearchTerm(barcode);

      // 自动生成批次号
      if (formData.auto_generate_batch) {
        const batchNumber = await generateBatchNumber(formData.batch_type, product.id);
        setFormData(prev => ({
          ...prev,
          batch_number: batchNumber
        }));
      }
    }
  };

  // 处理追溯码扫描
  const handleTraceCodeScan = async (code?: string) => {
    const scanCode = code || traceCode.trim();
    if (!scanCode) {
      showNotification('请输入追溯码', 'warning');
      return;
    }

    // 检查追溯码是否已经在清单中
    const isCodeExists = batchFormData.items.some(item =>
      item.trace_codes.includes(scanCode)
    );

    if (isCodeExists) {
      showNotification('该追溯码已在入库清单中，请勿重复添加', 'warning');
      setTraceCode('');
      return;
    }

    setIsScanning(true);
    try {
      // 首先检查追溯码是否已经被使用过
      const checkResponse = await fetch(`/api/inventory/check-trace-code?code=${encodeURIComponent(scanCode)}`);
      const checkData = await checkResponse.json();

      let isTraceCodeUsed = false;
      if (checkData.success && checkData.data.isUsed) {
        isTraceCodeUsed = true;
        showNotification(`该追溯码已被使用过，将只做本地入库，不上传到码上放心平台。\n使用记录：${checkData.data.records.length} 条`, 'warning');
      }
      const response = await fetch(`/api/drug-trace?code=${encodeURIComponent(scanCode)}`);
      const data = await response.json();

      if (data.success) {
        setScanResult(data);
        setScannedDrugInfo(data.data.drugInfo);

        // 从扫描结果中提取信息并自动填充
        const drugInfo = data.data.drugInfo;

        // 检查API响应的msg_info
        if (drugInfo?.result?.msg_info && drugInfo.result.msg_info !== '调用成功') {
          showNotification(`码上放心平台提示：${drugInfo.result.msg_info}`, 'warning');
        }

        if (drugInfo?.result?.models?.code_full_info_dto?.[0]) {
          const codeInfo = drugInfo.result.models.code_full_info_dto[0];
          const drugBaseInfo = codeInfo.drug_ent_base_d_t_o;

          // 尝试根据药品信息匹配本地药品
          let matchedProduct = null;
          if (drugBaseInfo?.physic_name) {
            // 先尝试按名称匹配
            matchedProduct = products.find(p =>
              p.name === drugBaseInfo.physic_name ||
              p.generic_name === drugBaseInfo.physic_name
            );

            // 如果没找到，尝试模糊匹配
            if (!matchedProduct) {
              matchedProduct = products.find(p =>
                p.name.includes(drugBaseInfo.physic_name) ||
                drugBaseInfo.physic_name.includes(p.name) ||
                (p.generic_name && (p.generic_name.includes(drugBaseInfo.physic_name) ||
                drugBaseInfo.physic_name.includes(p.generic_name)))
              );
            }
          }

          // 如果找到匹配的药品，直接添加到清单
          if (matchedProduct) {
            // 处理有效期 - 根据码上放心平台API结构解析
            let expiryDate = '';
            let batchNo = '';

            // 从 code_produce_info_d_t_o.produce_info_list.produce_info_dto[0] 获取信息
            const produceInfo = codeInfo.code_produce_info_d_t_o?.produce_info_list?.produce_info_dto?.[0];
            if (produceInfo) {
              // 有效期格式：YYYYMMDD -> YYYY-MM-DD
              if (produceInfo.expire_date) {
                const expireDateStr = produceInfo.expire_date.toString();
                if (expireDateStr.length === 8) {
                  const year = expireDateStr.substring(0, 4);
                  const month = expireDateStr.substring(4, 6);
                  const day = expireDateStr.substring(6, 8);
                  expiryDate = `${year}-${month}-${day}`;
                }
              }

              // 批次号
              batchNo = produceInfo.batch_no || '';
            }

            // 生成批次号（如果没有从API获取到）
            const batchNumber = batchNo || await generateBatchNumber('PC', matchedProduct.id);

            const newItem: Omit<StockInItem, 'id'> = {
              product_id: matchedProduct.id,
              product_name: matchedProduct.name,
              product_specification: matchedProduct.specification,
              quantity: 1,
              supplier_id: matchedProduct.supplier_id || 0,
              supplier_name: suppliers.find(s => s.id === matchedProduct.supplier_id)?.name,
              batch_number: batchNumber,
              auto_generate_batch: !codeInfo.batch_no,
              batch_type: 'PC',
              production_date: '', // 默认为空，用户可以手动填写
              expiry_date: expiryDate,
              cost_price: 0,
              trace_codes: [scanCode],
              notes: isTraceCodeUsed ? '追溯码已使用，仅本地入库' : ''
            };

            addItemToList(newItem);

            // 清空输入框
            setTraceCode('');

            showNotification(`追溯码扫描成功！已自动添加药品"${matchedProduct.name}"到入库清单。`, 'success');
          } else {
            // 如果没找到匹配的药品，提示用户手动添加
            showNotification(`追溯码扫描成功！\n药品名称：${drugBaseInfo?.physic_name || '未知'}\n请在下方手动搜索并添加对应的药品。`, 'warning');

            // 清空输入框
            setTraceCode('');
          }
        }
      } else {
        setScanResult(data);
        // 提取API返回的详细错误信息
        const errorMessage = data.data?.drugInfo?.result?.msg_info || data.message || '扫码失败';
        showNotification('扫码失败: ' + errorMessage, 'error');
      }
    } catch (error) {
      console.error('扫码失败:', error);
      showNotification('扫码失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error');
    } finally {
      setIsScanning(false);
    }
  };

  // 处理追溯码添加
  const handleAddTraceCode = (traceCode: string) => {
    if (!traceCode.trim()) return;

    const trimmedCode = traceCode.trim();
    if (formData.trace_codes.includes(trimmedCode)) {
      console.warn('该追溯码已存在');
      return;
    }

    setFormData(prev => ({
      ...prev,
      trace_codes: [...prev.trace_codes, trimmedCode]
    }));
  };

  // 处理追溯码删除
  const handleRemoveTraceCode = (index: number) => {
    setFormData(prev => ({
      ...prev,
      trace_codes: prev.trace_codes.filter((_, i) => i !== index)
    }));
  };

  // 处理批量追溯码输入
  const handleBatchTraceCodesInput = (codes: string) => {
    const codeList = codes.split(/[\n,，\s]+/)
      .map(code => code.trim())
      .filter(code => code.length > 0);

    const uniqueCodes = [...new Set([...formData.trace_codes, ...codeList])];

    setFormData(prev => ({
      ...prev,
      trace_codes: uniqueCodes
    }));
  };

  // 生成批次号（根据药品特性决定批次号格式）
  const generateBatchNumber = async (batchType: string, productId: number) => {
    if (!productId) return '';

    try {
      // 获取药品信息以决定批次号格式
      const productResponse = await fetch(`/api/products/${productId}`);
      const productData = await productResponse.json();

      if (productData.success && productData.data) {
        const product = productData.data;

        // 根据药品名称或剂型决定批次号格式
        const productName = product.name.toLowerCase();
        const dosageForm = product.dosage_form?.toLowerCase() || '';

        // 生成日期字符串（YYMMDD格式）
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const dateStr = year + month + day;

        // 生成随机序号（01-99）
        const sequence = Math.floor(Math.random() * 99) + 1;
        const sequenceStr = sequence.toString().padStart(2, '0');

        // 根据药品特性决定批次号格式
        if (productName.includes('胶囊') || productName.includes('片') || productName.includes('颗粒')) {
          // 口服制剂使用字母+数字格式：D250505
          const letters = ['D', 'C', 'B', 'A', 'E', 'F'];
          const letter = letters[Math.floor(Math.random() * letters.length)];
          return `${letter}${dateStr.slice(0, 4)}${sequenceStr}`;
        } else if (productName.includes('乳膏') || productName.includes('软膏') || productName.includes('凝胶')) {
          // 外用制剂使用纯数字格式：231003
          return `${dateStr.slice(0, 4)}${sequenceStr}`;
        } else if (productName.includes('注射') || productName.includes('针剂')) {
          // 注射剂使用特殊格式
          return `Z${dateStr.slice(0, 4)}${sequenceStr}`;
        } else {
          // 其他药品使用默认数字格式
          return `${dateStr.slice(0, 4)}${sequenceStr}`;
        }
      }
    } catch (error) {
      console.error('生成批次号失败:', error);
    }

    // 如果获取药品信息失败，生成一个简单的批次号
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const sequence = Math.floor(Math.random() * 99) + 1;
    return `${year}${month}${sequence.toString().padStart(2, '0')}`;
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (name === 'quantity' || name === 'cost_price') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // 产品选择已由专门的handleProductChange处理
    }
  };

  // 处理产品选择变化
  const handleProductChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productId = parseInt(e.target.value);

    // 找到选中的产品并设置选中状态
    const product = filteredProducts.find(p => p.id === productId);
    setSelectedProduct(product || null);

    setFormData(prev => ({
      ...prev,
      product_id: productId,
      supplier_id: product?.supplier_id || 0
    }));

    // 如果选择了有效的产品，生成批次号
    if (productId > 0) {
      const batchNumber = await generateBatchNumber(formData.batch_type, productId);
      setFormData(prev => ({
        ...prev,
        batch_number: batchNumber
      }));
    } else {
      // 清空批次号
      setFormData(prev => ({
        ...prev,
        batch_number: ''
      }));
    }
  };

  // 处理批次类型变化
  const handleBatchTypeChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const batchType = e.target.value;

    setFormData(prev => ({
      ...prev,
      batch_type: batchType
    }));

    // 如果已选择产品，重新生成批次号
    if (formData.product_id > 0) {
      const batchNumber = await generateBatchNumber(batchType, formData.product_id);
      setFormData(prev => ({
        ...prev,
        batch_number: batchNumber
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (batchFormData.items.length === 0) {
      showNotification('请至少添加一种药品到入库清单', 'warning');
      return;
    }

    // 验证每个药品项目
    for (const item of batchFormData.items) {
      if (!item.supplier_id) {
        showNotification(`药品"${item.product_name}"未选择供应商，请完善信息`, 'warning');
        return;
      }
      if (item.quantity <= 0) {
        showNotification(`药品"${item.product_name}"的数量必须大于0`, 'warning');
        return;
      }
    }

    try {
      // 批量提交所有药品入库
      for (const item of batchFormData.items) {
        const stockInData = {
          product_id: item.product_id,
          quantity: item.quantity,
          supplier_id: item.supplier_id,
          stock_in_date: batchFormData.stock_in_date,
          batch_number: item.batch_number,
          auto_generate_batch: item.auto_generate_batch,
          batch_type: item.batch_type,
          production_date: item.production_date,
          expiry_date: item.expiry_date,
          cost_price: item.cost_price,
          notes: item.notes || batchFormData.global_notes,
          trace_codes: item.trace_codes,
          upload_to_mashangfangxin: batchFormData.upload_to_mashangfangxin
        };

        const result = await onSubmit(stockInData);

        // 如果启用了码上放心平台上传且有追溯码，且追溯码未被使用过，则上传单据
        const hasUsedTraceCode = item.notes && item.notes.includes('追溯码已使用');
        if (batchFormData.upload_to_mashangfangxin && item.trace_codes.length > 0 && !hasUsedTraceCode) {
          try {
            console.log('开始上传入库单据到码上放心平台...');

            const uploadResult = await apiPostJson<any>('/api/inventory/upload-bill', {
              type: 'in',
              inventory_id: (result as any)?.inventory_id || (result as any)?.data?.inventory_id || (result as any)?.id,
              trace_codes: item.trace_codes,
            });

            if (uploadResult.success) {
              console.log('入库单据上传到码上放心平台成功:', uploadResult.data);
              showNotification(`药品"${item.product_name}"已上传 ${item.trace_codes.length} 个追溯码到码上放心平台。单据号：${uploadResult.data.bill_code}`, 'success');
            } else {
              console.error('上传到码上放心平台失败:', uploadResult.message);
              showNotification(`药品"${item.product_name}"入库成功，但上传到码上放心平台失败：${uploadResult.message}`, 'warning');
            }
          } catch (uploadError) {
            console.error('上传到码上放心平台时发生错误:', uploadError);
            showNotification(`药品"${item.product_name}"入库成功，但上传到码上放心平台时发生错误`, 'warning');
          }
        }
      }

      showNotification(`批量入库成功！共入库 ${batchFormData.items.length} 种药品`, 'success');
      setTimeout(() => {
        onClose();
      }, 3000); // 延迟关闭，让用户看到成功提示
    } catch (error) {
      console.error('批量入库失败:', error);
      showNotification('批量入库失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error');
    }
  };

  // 生成临时ID
  const generateTempId = () => {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };

  // 添加药品到清单
  const addItemToList = (item: Omit<StockInItem, 'id'>) => {
    // 检查是否存在相同药品、供应商、成本价、批次号的项目
    const existingItemIndex = batchFormData.items.findIndex(existingItem =>
      existingItem.product_id === item.product_id &&
      existingItem.supplier_id === item.supplier_id &&
      existingItem.cost_price === item.cost_price &&
      existingItem.batch_number === item.batch_number &&
      existingItem.production_date === item.production_date &&
      existingItem.expiry_date === item.expiry_date
    );

    if (existingItemIndex !== -1) {
      // 如果找到相同的项目，合并数量和追溯码
      setBatchFormData(prev => ({
        ...prev,
        items: prev.items.map((existingItem, index) => {
          if (index === existingItemIndex) {
            return {
              ...existingItem,
              quantity: existingItem.quantity + item.quantity,
              trace_codes: [...new Set([...existingItem.trace_codes, ...item.trace_codes])]
            };
          }
          return existingItem;
        })
      }));

      showNotification(`已合并到现有药品项目中，数量增加 ${item.quantity}`, 'success');
    } else {
      // 如果没有找到相同的项目，添加新项目
      const newItem: StockInItem = {
        ...item,
        id: generateTempId()
      };

      setBatchFormData(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }
  };

  // 更新清单中的药品
  const updateItemInList = async (itemId: string, updates: Partial<StockInItem>) => {
    setBatchFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, ...updates };

          // 如果更新了批次类型且启用自动生成，重新生成批次号
          if (updates.batch_type && updatedItem.auto_generate_batch) {
            generateBatchNumber(updatedItem.batch_type, updatedItem.product_id).then(batchNumber => {
              setBatchFormData(prev => ({
                ...prev,
                items: prev.items.map(i =>
                  i.id === itemId ? { ...i, batch_number: batchNumber } : i
                )
              }));
            });
          }

          return updatedItem;
        }
        return item;
      })
    }));
  };

  // 从清单中删除药品
  const removeItemFromList = (itemId: string) => {
    setBatchFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  // 处理追溯码输入（支持扫码枪）
  const handleTraceCodeInput = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 扫码枪通常以回车结束
    if (e.key === 'Enter') {
      e.preventDefault(); // 阻止表单提交
      const code = (e.target as HTMLInputElement).value.trim();
      if (code) {
        await handleTraceCodeScan(code);
      }
    }
  };

  return (
    <>
      <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      {/* 通知组件 - 动态大小调整 */}
      {notification.show && (
        <div className="fixed top-4 right-4 z-[60] max-w-sm sm:max-w-md lg:max-w-lg">
          <div className={`p-4 rounded-lg shadow-lg border-l-4 min-w-80 max-w-full ${
            notification.type === 'success' ? 'bg-green-50 border-green-500 text-green-800' :
            notification.type === 'error' ? 'bg-red-50 border-red-500 text-red-800' :
            notification.type === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-800' :
            'bg-blue-50 border-blue-500 text-blue-800'
          }`}>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {notification.type === 'success' && <span className="text-green-500">✅</span>}
                {notification.type === 'error' && <span className="text-red-500">❌</span>}
                {notification.type === 'warning' && <span className="text-yellow-500">⚠️</span>}
                {notification.type === 'info' && <span className="text-blue-500">ℹ️</span>}
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-medium whitespace-pre-line break-words">{notification.message}</p>
              </div>
              <button
                onClick={() => setNotification(prev => ({ ...prev, show: false }))}
                className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-6xl w-full bg-white rounded-xl shadow-lg max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <Dialog.Title className="text-xl font-bold text-blue-700 mb-6">
              批量药品入库
            </Dialog.Title>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 快速扫码添加区域 */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-700 mb-3 flex items-center">
                  <span className="mr-2">📱</span>
                  快速扫码添加
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  扫描药品追溯码可快速添加药品到入库清单，自动填充有效期和批次号
                </p>

                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={traceCode}
                    onChange={(e) => setTraceCode(e.target.value)}
                    onKeyDown={handleTraceCodeInput}
                    placeholder="请扫描或输入药品追溯码"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                  <button
                    type="button"
                    onClick={() => handleTraceCodeScan()}
                    disabled={isScanning || !traceCode.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isScanning ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-1 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        识别中...
                      </>
                    ) : (
                      '🔍 添加到清单'
                    )}
                  </button>
                </div>
              </div>

              {/* 入库清单 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-blue-700 flex items-center">
                    <span className="mr-2">📋</span>
                    入库清单 ({batchFormData.items.length} 种药品)
                  </h3>
                  <button
                    type="button"
                    onClick={() => setIsAddingItem(true)}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
                  >
                    <span className="mr-1">➕</span>
                    手动添加药品
                  </button>
                </div>

                {batchFormData.items.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-2">📦</div>
                    <p>暂无药品，请扫码或手动添加药品到入库清单</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {(() => {
                      // 按药品分组显示
                      const groupedItems = batchFormData.items.reduce((groups, item) => {
                        const key = item.product_id;
                        if (!groups[key]) {
                          groups[key] = [];
                        }
                        groups[key].push(item);
                        return groups;
                      }, {} as Record<number, StockInItem[]>);

                      return Object.entries(groupedItems).map(([productId, items]) => (
                        <div key={productId} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                          {/* 药品标题 */}
                          <div className="bg-blue-50 px-4 py-2 border-b border-blue-100">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="text-blue-700 font-medium text-lg">{items[0].product_name}</h4>
                                {items[0].product_specification && (
                                  <p className="text-sm text-gray-600">{items[0].product_specification}</p>
                                )}
                              </div>
                              <div className="text-sm text-blue-600">
                                {items.length > 1 ? `${items.length} 个批次` : '1 个批次'}
                              </div>
                            </div>
                          </div>

                          {/* 批次列表 */}
                          <div className="divide-y divide-gray-100">
                            {items.map((item, batchIndex) => (
                              <div key={item.id} className="p-4">
                                <div className="flex justify-between items-start">
                                  <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 mb-1">
                                        {items.length > 1 ? `批次 ${batchIndex + 1}` : '药品信息'}
                                      </label>
                                      <div className="text-blue-700 font-medium">{item.product_name}</div>
                                      {item.product_specification && (
                                        <div className="text-xs text-gray-500">{item.product_specification}</div>
                                      )}
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 mb-1">数量</label>
                                      <input
                                        type="number"
                                        value={item.quantity}
                                        onChange={(e) => updateItemInList(item.id, { quantity: parseInt(e.target.value) || 1 })}
                                        min="1"
                                        className="w-full px-2 py-1 border border-gray-300 rounded text-blue-700 text-sm"
                                      />
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 mb-1">供应商</label>
                                      <select
                                        value={item.supplier_id}
                                        onChange={(e) => {
                                          const supplierId = parseInt(e.target.value);
                                          const supplier = suppliers.find(s => s.id === supplierId);
                                          updateItemInList(item.id, {
                                            supplier_id: supplierId,
                                            supplier_name: supplier?.name
                                          });
                                        }}
                                        className="w-full px-2 py-1 border border-gray-300 rounded text-blue-700 text-sm"
                                      >
                                        <option value={0}>请选择供应商</option>
                                        {suppliers.map(supplier => (
                                          <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                                        ))}
                                      </select>
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-500 mb-1">成本价</label>
                                      <input
                                        type="number"
                                        value={item.cost_price}
                                        onChange={(e) => updateItemInList(item.id, { cost_price: parseFloat(e.target.value) || 0 })}
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        className="w-full px-2 py-1 border border-gray-300 rounded text-blue-700 text-sm"
                                      />
                                    </div>
                                  </div>
                                  <button
                                    type="button"
                                    onClick={() => removeItemFromList(item.id)}
                                    className="ml-4 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                                    title="删除此批次"
                                  >
                                    🗑️
                                  </button>
                                </div>

                                {/* 批次详情展开区域 */}
                                <div className="mt-3">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      const expandedItems = new Set(expandedBatchItems);
                                      if (expandedItems.has(item.id)) {
                                        expandedItems.delete(item.id);
                                      } else {
                                        expandedItems.add(item.id);
                                      }
                                      setExpandedBatchItems(expandedItems);
                                    }}
                                    className="flex items-center justify-between w-full p-2 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                                  >
                                    <div className="flex items-center space-x-4 text-sm">
                                      <div>
                                        <span className="text-gray-500">批次号：</span>
                                        <span className="text-blue-700 font-mono">{item.batch_number || '待生成'}</span>
                                      </div>
                                      <div>
                                        <span className="text-gray-500">生产日期：</span>
                                        <span className="text-blue-700">{item.production_date || '未设置'}</span>
                                      </div>
                                      <div>
                                        <span className="text-gray-500">有效期：</span>
                                        <span className="text-blue-700">{item.expiry_date || '未设置'}</span>
                                      </div>
                                      <div>
                                        <span className="text-gray-500">追溯码：</span>
                                        <span className="text-blue-700">{item.trace_codes.length > 0 ? `${item.trace_codes.length}个` : '无'}</span>
                                      </div>
                                    </div>
                                    <div className="text-gray-400">
                                      {expandedBatchItems.has(item.id) ? '▼' : '▶'}
                                    </div>
                                  </button>

                                  {/* 展开的详细信息 */}
                                  {expandedBatchItems.has(item.id) && (
                                    <div className="mt-3 p-3 bg-gray-50 rounded-md space-y-3">
                                      {/* 生产日期编辑 */}
                                      <div>
                                        <label className="block text-xs font-medium text-gray-500 mb-1">生产日期</label>
                                        <input
                                          type="date"
                                          value={item.production_date}
                                          onChange={(e) => updateItemInList(item.id, { production_date: e.target.value })}
                                          className="w-full px-2 py-1 border border-gray-300 rounded text-blue-700 text-sm"
                                        />
                                      </div>

                                      {/* 有效期编辑 */}
                                      <div>
                                        <label className="block text-xs font-medium text-gray-500 mb-1">有效期</label>
                                        <input
                                          type="date"
                                          value={item.expiry_date}
                                          onChange={(e) => updateItemInList(item.id, { expiry_date: e.target.value })}
                                          className="w-full px-2 py-1 border border-gray-300 rounded text-blue-700 text-sm"
                                        />
                                      </div>

                                      {/* 追溯码列表 */}
                                      {item.trace_codes.length > 0 && (
                                        <div>
                                          <label className="block text-xs font-medium text-gray-500 mb-1">追溯码列表</label>
                                          <div className="space-y-1 max-h-32 overflow-y-auto">
                                            {item.trace_codes.map((code, codeIndex) => (
                                              <div key={codeIndex} className="flex items-center justify-between bg-white p-2 rounded border">
                                                <span className="text-blue-700 font-mono text-xs">{code}</span>
                                                <button
                                                  type="button"
                                                  onClick={() => {
                                                    const newCodes = item.trace_codes.filter((_, i) => i !== codeIndex);
                                                    updateItemInList(item.id, { trace_codes: newCodes });
                                                  }}
                                                  className="text-red-500 hover:text-red-700 text-xs"
                                                  title="删除此追溯码"
                                                >
                                                  ✕
                                                </button>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ));
                    })()}
                  </div>
                )}
              </div>

              {/* 入库设置 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-700 mb-4">入库设置</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      入库日期 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={batchFormData.stock_in_date}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, stock_in_date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      整体备注
                    </label>
                    <input
                      type="text"
                      value={batchFormData.global_notes}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, global_notes: e.target.value }))}
                      placeholder="可选：为本次入库添加整体备注"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={batchFormData.upload_to_mashangfangxin}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, upload_to_mashangfangxin: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-blue-700">
                      上传入库单据到码上放心平台
                    </span>
                  </label>
                  <p className="text-xs text-gray-600 mt-1 ml-6">
                    勾选此项将在入库完成后自动上传单据到码上放心平台进行药品追溯
                  </p>
                </div>
              </div>

              {/* 按钮区域 */}
              <div className="flex justify-between items-center pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  共 {batchFormData.items.length} 种药品，总数量：{batchFormData.items.reduce((sum, item) => sum + item.quantity, 0)}
                </div>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={batchFormData.items.length === 0}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    保存
                  </button>
                </div>
              </div>
            </form>
          </div>
        </Dialog.Panel>
      </div>
      </Dialog>

      {/* 手动添加药品模态框 */}
      {isAddingItem && (
        <Dialog open={isAddingItem} onClose={() => setIsAddingItem(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-xl shadow-xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <Dialog.Title className="text-lg font-medium text-blue-700 mb-4">
                手动添加药品到入库清单
              </Dialog.Title>

              {/* 搜索方式选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-black mb-2">搜索方式</label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="name"
                      checked={searchMethod === 'name'}
                      onChange={(e) => setSearchMethod(e.target.value as 'name')}
                      className="mr-2"
                    />
                    <span className="text-sm text-black">按药品名称</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="barcode"
                      checked={searchMethod === 'barcode'}
                      onChange={(e) => setSearchMethod(e.target.value as 'barcode')}
                      className="mr-2"
                    />
                    <span className="text-sm text-black">按药品标识码</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="category"
                      checked={searchMethod === 'category'}
                      onChange={(e) => setSearchMethod(e.target.value as 'category')}
                      className="mr-2"
                    />
                    <span className="text-sm text-black">按分类浏览</span>
                  </label>
                </div>
              </div>

              {/* 搜索输入 */}
              {searchMethod === 'name' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-black mb-1">药品名称</label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="输入药品名称进行搜索..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                  />
                </div>
              )}

              {searchMethod === 'barcode' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-black mb-1">药品标识码</label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => {
                      // 限制输入长度为7位数字
                      const value = e.target.value.replace(/\D/g, '').slice(0, 7);
                      setSearchTerm(value);
                    }}
                    placeholder="输入或扫描药品标识码（7位数字）..."
                    maxLength={7}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    药品标识码为7位数字，扫描追溯码时会自动提取前7位
                  </p>
                </div>
              )}

              {searchMethod === 'category' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-black mb-1">药品分类</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                  >
                    <option value={0}>请选择分类</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* 药品列表 */}
              {filteredProducts.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-black mb-2">选择药品</label>
                  <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                    {filteredProducts.map(product => (
                      <div
                        key={product.id}
                        onClick={() => setSelectedProduct(product)}
                        className={`p-3 cursor-pointer hover:bg-blue-50 border-b border-gray-200 last:border-b-0 ${
                          selectedProduct?.id === product.id ? 'bg-blue-100' : ''
                        }`}
                      >
                        <div className="font-medium text-blue-700">{product.name}</div>
                        <div className="text-sm text-gray-600">{product.specification} | {product.manufacturer}</div>
                        <div className="text-xs text-gray-500">库存: {product.stock_quantity}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 按钮 */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsAddingItem(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    if (selectedProduct) {
                      // 创建新的入库项目
                      const newItem: Omit<StockInItem, 'id'> = {
                        product_id: selectedProduct.id,
                        product_name: selectedProduct.name,
                        product_specification: selectedProduct.specification,
                        quantity: 1,
                        supplier_id: selectedProduct.supplier_id || 0,
                        supplier_name: selectedProduct.supplier_name || '',
                        batch_number: '',
                        auto_generate_batch: true,
                        batch_type: 'PC',
                        production_date: '', // 默认为空，用户可以手动填写
                        expiry_date: '',
                        cost_price: 0,
                        trace_codes: [],
                        notes: ''
                      };

                      addItemToList(newItem);
                      setIsAddingItem(false);
                      setSelectedProduct(null);
                      setSearchTerm('');
                      showNotification(`已添加药品"${selectedProduct.name}"到入库清单`, 'success');
                    }
                  }}
                  disabled={!selectedProduct}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  添加到清单
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
        </Dialog>
      )}
    </>
  );
}