import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { NextRequest } from 'next/server';

vi.mock('@/lib/db', async () => {
  return {
    get: vi.fn(),
    query: vi.fn(),
    run: vi.fn(),
  };
});

// 动态导入以获取最新实现
async function loadHandler() {
  const mod = await import('../../../app/api/sales/scan-trace-code/route');
  return mod;
}

function makeRequest(body: any): NextRequest {
  const url = new URL('http://localhost/api/sales/scan-trace-code');
  const req = {
    json: async () => body,
    nextUrl: url,
  } as any as NextRequest;
  return req;
}

// mock fetch -> /api/mashangfangxin
const mockFetch = vi.fn();
(global as any).fetch = mockFetch;

const { get } = vi.mocked(await import('@/lib/db')) as any;

describe('/api/sales/scan-trace-code', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('缺少追溯码参数返回400', async () => {
    const { POST } = await loadHandler();
    const res = await POST(makeRequest({}));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(400);
    expect(data.success).toBe(false);
  });

  it('企业ID未设置错误透传', async () => {
    const { POST } = await loadHandler();

    // 未使用追溯码前检查：先无使用记录
    (get as any).mockResolvedValueOnce(null);

    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      json: async () => ({ success: false, message: '企业ID未设置，请在系统设置中配置码上放心开放平台企业ID' }),
    });

    const res = await POST(makeRequest({ traceCode: '84084700665075883011' }));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(400);
    expect(data.message).toMatch(/企业ID未设置/);
  });

  it('追溯码已被使用', async () => {
    const { POST } = await loadHandler();

    // 第一次 get -> 查询使用记录，返回已使用
    (get as any).mockResolvedValueOnce({ 操作类型: '销售', 操作时间: '2025-07-08 10:30:00' });

    const res = await POST(makeRequest({ traceCode: '84084700665075883011' }));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(400);
    expect(data.message).toMatch(/已被使用/);
  });

  it('库存不足', async () => {
    const { POST } = await loadHandler();

    // 1) 使用记录检查
    (get as any).mockResolvedValueOnce(null);

    // 2) 码上放心 OK，返回数据结构
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: { result: { models: { code_full_info_dto: [ {
        batch_no: 'B001', expire_date: '2027-01-01', production_date: '2025-01-01', barcode: '690xxx',
        drug_ent_base_d_t_o: { ent_name: '厂商A', physic_name: '药品A', pkg_spec_crit: '0.25g*24', approval_licence_no: '*********' }
      } ] } } } }),
    });

    // 3) 本地药品匹配：按药品标识码未命中 -> 批准文号命中
    (get as any).mockResolvedValueOnce(null); // 药品标识码
    (get as any).mockResolvedValueOnce({ 编号: 1, 名称: '药品A', 通用名: '阿莫', 规格: '0.25g*24', 生产厂家: '厂商A', 售价: 12.5, 批准文号: '*********' });

    // 4) 库存查询：药品库存 0
    (get as any).mockResolvedValueOnce({ qty: 0 });

    const res = await POST(makeRequest({ traceCode: '84084700665075883011' }));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(400);
    expect(data.message).toMatch(/库存不足/);
  });

  it('成功（按批次号匹配）', async () => {
    const { POST } = await loadHandler();

    (get as any).mockResolvedValueOnce(null); // 使用记录

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: { result: { models: { code_full_info_dto: [ {
        batch_no: 'B001', expire_date: '2027-01-01', production_date: '2025-01-01', barcode: '',
        drug_ent_base_d_t_o: { ent_name: '厂商A', physic_name: '药品A', pkg_spec_crit: '0.25g*24', approval_licence_no: '*********' }
      } ] } } } }),
    });

    // 本地药品匹配成功
    (get as any).mockResolvedValueOnce({ 编号: 1, 名称: '药品A', 通用名: '阿莫', 规格: '0.25g*24', 生产厂家: '厂商A', 售价: 12.5, 批准文号: '*********' });

    // 库存查询：药品库存 10
    (get as any).mockResolvedValueOnce({ qty: 10 });

    // 批次查找：按批次号命中
    (get as any).mockResolvedValueOnce({ 编号: 9, 当前数量: 3 });

    const res = await POST(makeRequest({ traceCode: '84084700665075883011' }));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.batchInfo.localBatchId).toBe(9);
    expect(data.data.batchInfo.localBatchQuantity).toBe(3);
  });

  it('成功（FIFO 选择）', async () => {
    const { POST } = await loadHandler();

    (get as any).mockResolvedValueOnce(null); // 使用记录

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: { result: { models: { code_full_info_dto: [ {
        batch_no: '', expire_date: '2027-01-01', production_date: '2025-01-01', barcode: '',
        drug_ent_base_d_t_o: { ent_name: '厂商A', physic_name: '药品A', pkg_spec_crit: '0.25g*24', approval_licence_no: '*********' }
      } ] } } } }),
    });

    // 本地药品匹配成功
    (get as any).mockResolvedValueOnce({ 编号: 1, 名称: '药品A', 通用名: '阿莫', 规格: '0.25g*24', 生产厂家: '厂商A', 售价: 12.5, 批准文号: '*********' });

    // 库存查询：药品库存 10
    (get as any).mockResolvedValueOnce({ qty: 10 });

    // 批次查找：批次号未提供 -> FIFO：返回一个批次
    (get as any).mockResolvedValueOnce({ 编号: 8, 当前数量: 5 });

    const res = await POST(makeRequest({ traceCode: '84084700665075883011' }));
    const data: any = await (res as any).json();
    expect((res as any).status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.batchInfo.localBatchId).toBe(8);
    expect(data.data.batchInfo.localBatchQuantity).toBe(5);
  });
});

